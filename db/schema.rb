# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_06_09_151520) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "accounting_debit_card_purchases", force: :cascade do |t|
    t.datetime "transaction_at", null: false
    t.integer "status", default: 0, null: false
    t.string "merchant_name", null: false
    t.bigint "vendor_id"
    t.string "description", null: false
    t.string "paid_by", null: false
    t.integer "amount_cents", default: 0, null: false
    t.bigint "bank_account_id", null: false
    t.string "card_id", null: false
    t.bigint "invoice_id"
    t.bigint "account_id"
    t.bigint "property_id"
    t.string "markup_kind"
    t.decimal "markup_raw"
    t.string "forward_to_type"
    t.bigint "forward_to_id"
    t.datetime "archived_at"
    t.bigint "tunisia_transaction_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lock_version"
    t.index ["account_id"], name: "index_accounting_debit_card_purchases_on_account_id"
    t.index ["bank_account_id"], name: "index_accounting_debit_card_purchases_on_bank_account_id"
    t.index ["forward_to_type", "forward_to_id"], name: "index_accounting_debit_card_purchases_on_forward_to"
    t.index ["invoice_id"], name: "index_accounting_debit_card_purchases_on_invoice_id"
    t.index ["property_id"], name: "index_accounting_debit_card_purchases_on_property_id"
    t.index ["vendor_id"], name: "index_accounting_debit_card_purchases_on_vendor_id"
  end

  create_table "accounting_missing_receipt_affidavits", force: :cascade do |t|
    t.bigint "debit_card_purchase_id", null: false
    t.text "items_purchased", null: false
    t.text "purpose_of_expense", null: false
    t.text "reason_for_missing_receipt", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["debit_card_purchase_id"], name: "index_missing_receipt_affidavits_on_dcp"
  end

  create_table "accounting_statements", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.datetime "approved_at", precision: nil
    t.bigint "approved_by_id"
    t.string "notes"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "type", default: "Maintenance::WorkPerformedStatement", null: false
    t.index ["approved_by_id"], name: "index_accounting_statements_on_approved_by_id"
    t.index ["company_id"], name: "index_accounting_statements_on_company_id"
  end

  create_table "action_mailbox_inbound_emails", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.string "message_id", null: false
    t.string "message_checksum", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["message_id", "message_checksum"], name: "index_action_mailbox_inbound_emails_uniqueness", unique: true
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_admin_comments", id: :serial, force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_id", null: false
    t.string "resource_type", null: false
    t.string "author_type"
    t.integer "author_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author_type_and_author_id"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource_type_and_resource_id"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", precision: nil, null: false
    t.string "service_name", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "addresses", id: :serial, force: :cascade do |t|
    t.string "line_one", null: false
    t.string "line_two"
    t.string "city", null: false
    t.string "postal_code", null: false
    t.string "region", null: false
    t.string "country", default: "United States"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "addressable_id", null: false
    t.string "addressable_type", null: false
    t.float "latitude"
    t.float "longitude"
    t.string "sub_region"
    t.integer "kind", default: 0, null: false
    t.index "to_tsvector('simple'::regconfig, (line_one)::text)", name: "addresses_line_one_gin", using: :gin
    t.index ["addressable_id", "addressable_type", "kind"], name: "index_addresses_on_addressable_id_and_addressable_type_and_kind"
    t.index ["addressable_id"], name: "index_addresses_on_addressable_id"
    t.index ["addressable_type", "addressable_id"], name: "index_addresses_on_addressable_type_and_addressable_id"
  end

  create_table "admin_user_customer_accesses", force: :cascade do |t|
    t.bigint "admin_user_id", null: false
    t.bigint "customer_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_admin_user_customer_accesses_on_admin_user_id"
    t.index ["customer_id"], name: "index_admin_user_customer_accesses_on_customer_id"
  end

  create_table "admin_users", id: :serial, force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "first_name", default: "Admin", null: false
    t.string "last_name", default: "User", null: false
    t.boolean "top_level", default: false, null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "agreements_agreement_types", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "slug", null: false
    t.bigint "document_template_id"
    t.index ["document_template_id"], name: "index_agreements_agreement_types_on_document_template_id"
    t.index ["name"], name: "index_agreements_agreement_types_on_name", unique: true
    t.index ["slug"], name: "index_agreements_agreement_types_on_slug", unique: true
  end

  create_table "agreements_simple_agreement_memberships", force: :cascade do |t|
    t.bigint "simple_agreement_id", null: false
    t.bigint "tenant_id", null: false
    t.integer "role", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role"], name: "index_agreements_simple_agreement_memberships_on_role"
    t.index ["simple_agreement_id"], name: "index_memberships_on_simple_agreements"
    t.index ["tenant_id"], name: "index_agreements_simple_agreement_memberships_on_tenant_id"
  end

  create_table "agreements_simple_agreements", force: :cascade do |t|
    t.bigint "agreement_type_id", null: false
    t.date "start_date"
    t.date "end_date"
    t.datetime "archived_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "property_id"
    t.bigint "company_id", null: false
    t.datetime "executed_at", precision: nil
    t.index ["agreement_type_id"], name: "index_agreements_simple_agreements_on_agreement_type_id"
    t.index ["company_id"], name: "index_agreements_simple_agreements_on_company_id"
    t.index ["property_id"], name: "index_agreements_simple_agreements_on_property_id"
  end

  create_table "alliance_transactions", force: :cascade do |t|
    t.bigint "payment_id", null: false
    t.string "alliance_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alliance_id"], name: "index_alliance_transactions_on_alliance_id", unique: true
    t.index ["payment_id"], name: "index_alliance_transactions_on_payment_id"
  end

  create_table "api_v2_keys", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.string "token_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.string "notes"
    t.datetime "last_used_at", precision: nil
    t.integer "use_count", default: 0, null: false
    t.boolean "include_pii", default: false, null: false
    t.index ["customer_id"], name: "index_api_v2_keys_on_customer_id"
    t.index ["user_id"], name: "index_api_v2_keys_on_user_id"
  end

  create_table "api_v2_owner_statement_entries", force: :cascade do |t|
    t.bigint "entity_id", null: false
    t.bigint "property_id", null: false
    t.date "date", null: false
    t.string "description", null: false
    t.string "contact"
    t.string "reference"
    t.bigint "cash_in_cents", default: 0, null: false
    t.bigint "cash_out_cents", default: 0, null: false
    t.bigint "balance_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_id"], name: "index_api_v2_owner_statement_entries_on_entity_id"
    t.index ["property_id"], name: "index_api_v2_owner_statement_entries_on_property_id"
  end

  create_table "approvals_approvals", force: :cascade do |t|
    t.string "approvable_type", null: false
    t.bigint "approvable_id", null: false
    t.string "approved_by_type", null: false
    t.bigint "approved_by_id", null: false
    t.bigint "rule_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "request_id"
    t.string "comment"
    t.string "type", null: false
    t.index ["approvable_type", "approvable_id"], name: "index_approvals_on_approvable"
    t.index ["approved_by_type", "approved_by_id"], name: "index_approvals_on_approved_by"
    t.index ["request_id"], name: "index_approvals_approvals_on_request_id"
    t.index ["rule_id"], name: "index_approvals_approvals_on_rule_id"
    t.index ["type"], name: "index_approvals_approvals_on_type"
  end

  create_table "approvals_change_requests", force: :cascade do |t|
    t.string "approvable_type", null: false
    t.bigint "approvable_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "requested_from_id", null: false
    t.boolean "pending", default: true, null: false
    t.string "message", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approvable_type", "approvable_id"], name: "index_approvals_change_requests_on_approvable"
    t.index ["created_by_id"], name: "index_approvals_change_requests_on_created_by_id"
    t.index ["requested_from_id"], name: "index_approvals_change_requests_on_requested_from_id"
  end

  create_table "approvals_requests", force: :cascade do |t|
    t.string "approver_type", null: false
    t.bigint "approver_id", null: false
    t.string "requested_by_type", null: false
    t.bigint "requested_by_id", null: false
    t.string "approvable_type", null: false
    t.bigint "approvable_id", null: false
    t.integer "action", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "token", null: false
    t.index ["approvable_type", "approvable_id"], name: "index_approvals_requests_on_approvable"
    t.index ["approver_type", "approver_id"], name: "index_approvals_requests_on_approver"
    t.index ["requested_by_type", "requested_by_id"], name: "index_approvals_requests_on_requested_by"
  end

  create_table "approvals_rules", force: :cascade do |t|
    t.string "name", null: false
    t.integer "action", null: false
    t.bigint "dependency_id"
    t.string "approver_sources", default: [], null: false, array: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "portfolio_ids", array: true
    t.string "seller_whitelist", array: true
    t.string "seller_blacklist", array: true
    t.index ["dependency_id"], name: "index_approvals_rules_on_dependency_id"
  end

  create_table "assignments", id: :serial, force: :cascade do |t|
    t.integer "assignable_id", null: false
    t.integer "user_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "assignable_type", null: false
    t.datetime "start_date", precision: nil
    t.datetime "end_date", precision: nil
    t.index ["assignable_id", "assignable_type"], name: "index_assignments_on_assignable_id_and_assignable_type"
  end

  create_table "attachments", id: :serial, force: :cascade do |t|
    t.string "parent_type"
    t.integer "parent_id"
    t.string "direct_upload_url"
    t.string "upload_file_name"
    t.string "upload_content_type"
    t.bigint "upload_file_size"
    t.datetime "upload_updated_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "type", default: "Photo", null: false
    t.string "uploaded_by_type"
    t.bigint "uploaded_by_id"
    t.jsonb "template_options"
    t.integer "template_id"
    t.json "upload_data"
    t.integer "order"
    t.integer "verification_document_kind", default: 0, null: false
    t.index ["parent_type", "parent_id"], name: "index_attachments_on_parent"
    t.index ["uploaded_by_type", "uploaded_by_id"], name: "index_attachments_on_uploaded_by_type_and_uploaded_by_id"
  end

  create_table "audits", force: :cascade do |t|
    t.integer "auditable_id"
    t.string "auditable_type"
    t.string "reference_name"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.string "action"
    t.jsonb "audited_changes"
    t.integer "version", default: 0
    t.string "comment"
    t.string "remote_address"
    t.string "request_uuid"
    t.datetime "created_at", precision: nil
    t.jsonb "audited_change_references"
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "background_checks", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "package_id", default: 1, null: false
    t.string "external_order_id"
    t.string "external_report_url"
    t.integer "status", default: 0, null: false
    t.text "encrypted_receipt"
    t.string "encrypted_receipt_iv"
    t.string "encrypted_receipt_salt"
    t.text "encrypted_result"
    t.string "encrypted_result_iv"
    t.string "encrypted_result_salt"
    t.bigint "lease_application_membership_id", null: false
    t.index ["lease_application_membership_id"], name: "index_background_checks_on_lease_application_membership_id"
  end

  create_table "bank_account_funds_transfers", force: :cascade do |t|
    t.bigint "bank_account_id", null: false
    t.bigint "processed_by_id", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.bigint "amount_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["bank_account_id"], name: "index_bank_account_funds_transfers_on_bank_account_id"
    t.index ["processed_by_id"], name: "index_bank_account_funds_transfers_on_processed_by_id"
  end

  create_table "bank_account_reconciliations", force: :cascade do |t|
    t.bigint "bank_account_id", null: false
    t.date "statement_date", null: false
    t.integer "statement_beginning_balance_cents", null: false
    t.integer "statement_ending_balance_cents", null: false
    t.bigint "submitted_by_id"
    t.datetime "submitted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["bank_account_id"], name: "index_bank_account_reconciliations_on_bank_account_id"
  end

  create_table "bank_accounts", id: :serial, force: :cascade do |t|
    t.string "owner_type", null: false
    t.integer "owner_id", null: false
    t.string "name", null: false
    t.string "routing_number", null: false
    t.string "account_number", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "account_type", default: 0, null: false
    t.string "profit_stars_id"
    t.string "zeamster_id"
    t.string "institution"
    t.bigint "ledger_account_id"
    t.bigint "merchant_account_id"
    t.string "pay_lease_id"
    t.integer "beginning_check_number"
    t.datetime "archived_at", precision: nil
    t.boolean "customer_managed", default: false, null: false
    t.integer "designation"
    t.index ["ledger_account_id"], name: "index_bank_accounts_on_ledger_account_id"
    t.index ["merchant_account_id"], name: "index_bank_accounts_on_merchant_account_id"
    t.index ["owner_type", "owner_id"], name: "index_bank_accounts_on_owner"
  end

  create_table "bank_branches", force: :cascade do |t|
    t.string "routing_number", null: false
    t.string "name", null: false
    t.string "line_one", null: false
    t.string "line_two"
    t.string "city", null: false
    t.string "region", null: false
    t.string "postal_code", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["routing_number"], name: "index_bank_branches_on_routing_number", unique: true
  end

  create_table "billings", force: :cascade do |t|
    t.bigint "invoice_id"
    t.bigint "project_id"
    t.bigint "estimate_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["estimate_id"], name: "index_billings_on_estimate_id"
    t.index ["invoice_id"], name: "index_billings_on_invoice_id"
    t.index ["project_id"], name: "index_billings_on_project_id"
  end

  create_table "boring_science_articles", id: :serial, force: :cascade do |t|
    t.string "blog", null: false
    t.string "title", null: false
    t.string "summary"
    t.text "body", null: false
    t.date "publication_date"
    t.string "slug", null: false
    t.string "author_type"
    t.integer "author_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["author_type", "author_id"], name: "index_boring_science_articles_on_author"
    t.index ["blog"], name: "index_boring_science_articles_on_blog"
    t.index ["slug"], name: "index_boring_science_articles_on_slug", unique: true
  end

  create_table "brands", id: :serial, force: :cascade do |t|
    t.string "logo_url", default: "/images/alever_logo.svg", null: false
    t.string "primary_color", default: "#66cca2", null: false
    t.string "secondary_color", default: "#59a686", null: false
    t.string "brandable_type", null: false
    t.integer "brandable_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "facebook"
    t.string "twitter"
    t.string "instagram"
    t.string "custom_styles"
    t.index ["brandable_type", "brandable_id"], name: "index_brands_on_brandable"
  end

  create_table "broadcasts", id: :serial, force: :cascade do |t|
    t.integer "author_id", null: false
    t.string "reply_to"
    t.string "subject", default: "", null: false
    t.string "body", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "broadcasts_recipients", id: :serial, force: :cascade do |t|
    t.integer "broadcast_id"
    t.string "recipient_type"
    t.integer "recipient_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "unread", default: true, null: false
    t.index ["broadcast_id"], name: "index_broadcasts_recipients_on_broadcast_id"
    t.index ["recipient_type", "recipient_id"], name: "index_broadcasts_recipients_on_recipient"
  end

  create_table "budget_amounts", id: :serial, force: :cascade do |t|
    t.integer "budget_id"
    t.integer "amount_cents", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "account_id", null: false
    t.date "month"
    t.string "type", default: "BudgetAmount::Monthly"
    t.bigint "project_id"
    t.index ["account_id"], name: "index_budget_amounts_on_account_id"
    t.index ["budget_id"], name: "index_budget_amounts_on_budget_id"
    t.index ["project_id"], name: "index_budget_amounts_on_project_id"
  end

  create_table "budgets", id: :serial, force: :cascade do |t|
    t.integer "property_id"
    t.integer "year", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "company_id", null: false
    t.index ["company_id"], name: "index_budgets_on_company_id"
    t.index ["property_id"], name: "index_budgets_on_property_id"
  end

  create_table "buildings", force: :cascade do |t|
    t.bigint "property_id", null: false
    t.string "name", null: false
    t.integer "square_feet", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["property_id"], name: "index_buildings_on_property_id"
  end

  create_table "calendar_events", force: :cascade do |t|
    t.string "title", null: false
    t.string "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "author_id", null: false
    t.datetime "start_date", precision: nil, null: false
    t.datetime "end_date", precision: nil, null: false
    t.integer "source", default: 0, null: false
    t.index ["author_id"], name: "index_calendar_events_on_author_id"
  end

  create_table "cash_pay_cards", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "external_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["tenant_id"], name: "index_cash_pay_cards_on_tenant_id"
  end

  create_table "charge_presets", force: :cascade do |t|
    t.integer "kind", default: 2, null: false
    t.string "name", null: false
    t.bigint "account_id", null: false
    t.integer "amount_cents", null: false
    t.integer "net_d", default: 5, null: false
    t.integer "grace_period", default: 0, null: false
    t.integer "priority", default: 2, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "configuration_id", null: false
    t.decimal "late_fee_amount_ratio"
    t.decimal "late_fee_balance_ratio"
    t.boolean "payment_plan_eligible", default: false, null: false
    t.integer "payment_plan_duration_days"
    t.integer "payment_plan_minimum_installment_count"
    t.integer "payment_plan_maximum_installment_count"
    t.index ["account_id"], name: "index_charge_presets_on_account_id"
    t.index ["configuration_id"], name: "index_charge_presets_on_configuration_id"
  end

  create_table "charge_presets_late_fees", force: :cascade do |t|
    t.bigint "charge_preset_id", null: false
    t.bigint "late_fee_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["charge_preset_id"], name: "index_charge_presets_late_fees_on_charge_preset_id"
    t.index ["late_fee_id"], name: "index_charge_presets_late_fees_on_late_fee_id"
  end

  create_table "charge_schedule_entries", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.integer "amount_cents", null: false
    t.boolean "recurring", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "account_id", null: false
    t.boolean "billed_separately", default: false, null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.bigint "charge_preset_id"
    t.bigint "charge_schedule_id", null: false
    t.boolean "inherit_term", default: true, null: false
    t.index ["account_id"], name: "index_charge_schedule_entries_on_account_id"
    t.index ["charge_preset_id"], name: "index_charge_schedule_entries_on_charge_preset_id"
    t.index ["charge_schedule_id"], name: "index_charge_schedule_entries_on_charge_schedule_id"
  end

  create_table "charge_schedule_entry_allocations", id: :serial, force: :cascade do |t|
    t.integer "entry_id", null: false
    t.integer "lease_membership_id", null: false
    t.integer "amount_cents", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["entry_id"], name: "index_charge_schedule_entry_allocations_on_entry_id"
    t.index ["lease_membership_id"], name: "index_charge_schedule_entry_allocations_on_lease_membership_id"
  end

  create_table "charge_schedules", force: :cascade do |t|
    t.string "chargeable_type", null: false
    t.bigint "chargeable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "proration", default: 0, null: false
    t.date "invoiced_through"
    t.index ["chargeable_type", "chargeable_id"], name: "index_charge_schedules_on_chargeable_type_and_chargeable_id"
  end

  create_table "charts_of_accounts", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.integer "accounts_receivable_id"
    t.integer "accounts_payable_id"
    t.integer "cash_account_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "description"
    t.bigint "default_maintenance_expense_account_id"
    t.bigint "default_maintenance_revenue_account_id"
    t.bigint "default_management_fees_expense_account_id"
    t.bigint "default_management_fees_revenue_account_id"
    t.bigint "clearing_account_id"
    t.bigint "prepaid_revenue_account_id"
    t.bigint "property_transfers_account_id"
    t.bigint "due_from_client_entity_account_id"
    t.bigint "due_to_customer_account_id"
    t.bigint "owner_cash_account_id"
    t.bigint "merchant_processing_revenue_account_id"
    t.bigint "prepaid_expense_account_id"
    t.bigint "security_deposit_account_id"
    t.bigint "management_held_security_deposit_account_id"
    t.bigint "default_management_leasing_commissions_expense_account_id"
    t.bigint "default_management_leasing_commissions_revenue_account_id"
    t.index ["clearing_account_id"], name: "index_charts_of_accounts_on_clearing_account_id"
    t.index ["default_maintenance_expense_account_id"], name: "index_charts_on_default_maintenance_expense_account"
    t.index ["default_maintenance_revenue_account_id"], name: "index_charts_on_default_maintenance_revenue_account"
    t.index ["default_management_fees_expense_account_id"], name: "index_charts_on_default_management_fees_expense_account"
    t.index ["default_management_fees_revenue_account_id"], name: "index_charts_on_default_management_fees_revenue_account"
    t.index ["default_management_leasing_commissions_expense_account_id"], name: "index_charts_on_default_leasing_commissions_expense_account"
    t.index ["default_management_leasing_commissions_revenue_account_id"], name: "index_charts_on_default_leasing_commissions_revenue_account"
    t.index ["management_held_security_deposit_account_id"], name: "index_management_deposits_on_charts_of_accounts"
    t.index ["merchant_processing_revenue_account_id"], name: "index_merchant_processing_on_charts_of_accounts"
    t.index ["prepaid_expense_account_id"], name: "index_charts_of_accounts_on_prepaid_expense_account_id"
    t.index ["prepaid_revenue_account_id"], name: "index_charts_of_accounts_on_prepaid_revenue_account_id"
    t.index ["security_deposit_account_id"], name: "index_charts_of_accounts_on_security_deposit_account_id"
  end

  create_table "chat_memberships", id: :serial, force: :cascade do |t|
    t.string "chat_type", null: false
    t.integer "chat_id", null: false
    t.string "user_type", null: false
    t.integer "user_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "chats", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.boolean "direct", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "collections_communications", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.integer "channel", null: false
    t.integer "balance_cents", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_collections_communications_on_tenant_id"
  end

  create_table "collections_demand_letter_batches", force: :cascade do |t|
    t.bigint "created_by_id", null: false
    t.boolean "prefer_electronic_delivery", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_collections_demand_letter_batches_on_created_by_id"
  end

  create_table "collections_demand_letters", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.bigint "demand_letter_batch_id"
    t.boolean "deliver_electronically", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "overdue_balance_cents", default: 0, null: false
    t.integer "overdue_rent_cents", default: 0, null: false
    t.integer "overdue_late_fees_cents", default: 0, null: false
    t.integer "overdue_other_cents", default: 0, null: false
    t.date "oldest_overdue_post_date", null: false
    t.date "oldest_overdue_due_date", null: false
    t.datetime "expires_at", precision: nil, null: false
    t.index ["demand_letter_batch_id"], name: "index_collections_demand_letters_on_demand_letter_batch_id"
    t.index ["lease_id"], name: "index_collections_demand_letters_on_lease_id"
  end

  create_table "collections_evictions", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.integer "overdue_balance_cents", null: false
    t.bigint "assigned_vendor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "created_by_id", null: false
    t.bigint "court_id"
    t.datetime "assigned_at", precision: nil
    t.datetime "closed_at", precision: nil
    t.date "date_filed"
    t.date "court_date"
    t.text "notes"
    t.integer "outcome"
    t.date "anticipated_date"
    t.index ["assigned_vendor_id"], name: "index_collections_evictions_on_assigned_vendor_id"
    t.index ["court_id"], name: "index_collections_evictions_on_court_id"
    t.index ["created_by_id"], name: "index_collections_evictions_on_created_by_id"
    t.index ["lease_id"], name: "index_collections_evictions_on_lease_id"
  end

  create_table "collections_opt_outs", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.integer "channel", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_collections_opt_outs_on_tenant_id"
  end

  create_table "companies", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "representative_id"
    t.string "contact_email"
    t.integer "customer_id"
    t.bigint "chart_of_accounts_id"
    t.date "locked_at"
    t.bigint "portfolio_id"
    t.integer "business_type"
    t.string "trade_name"
    t.string "state_of_incorporation"
    t.string "phone"
    t.boolean "setup", default: false, null: false
    t.boolean "customer_managed", default: true, null: false
    t.bigint "default_deposit_account_id"
    t.bigint "default_withdrawal_account_id"
    t.text "notes"
    t.boolean "purely_property_allocated", default: false, null: false
    t.date "management_fees_billed_through"
    t.datetime "archived_at", precision: nil
    t.datetime "self_archived_at", precision: nil
    t.bigint "archived_at_from_id"
    t.string "archived_at_from_type"
    t.integer "fiscal_year_ending_month", default: 12, null: false
    t.integer "fiscal_year_ending_day", default: 31, null: false
    t.integer "preferred_disbursement"
    t.string "remittance_instructions"
    t.datetime "accounting_updated_at", precision: nil
    t.string "tunisia_customer_id"
    t.string "zrm_account_id"
    t.index ["chart_of_accounts_id"], name: "index_companies_on_chart_of_accounts_id"
    t.index ["default_deposit_account_id"], name: "index_companies_on_default_deposit_account_id"
    t.index ["default_withdrawal_account_id"], name: "index_companies_on_default_withdrawal_account_id"
    t.index ["name"], name: "index_companies_on_name", unique: true
    t.index ["portfolio_id"], name: "index_companies_on_portfolio_id"
  end

  create_table "configurations", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.bigint "chart_of_accounts_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "primary_applicant_package_id"
    t.integer "rent_due_day", default: 1, null: false
    t.decimal "security_deposit_percentage_rent", default: "0.0", null: false
    t.integer "security_deposit_flat_amount_cents", default: 0, null: false
    t.integer "proration", default: 0, null: false
    t.integer "rent_pre_post", default: 0, null: false
    t.integer "primary_applicant_charge_id"
    t.integer "co_applicant_charge_id"
    t.integer "guarantor_charge_id"
    t.integer "occupant_charge_id"
    t.integer "co_applicant_package_id"
    t.integer "guarantor_package_id"
    t.integer "occupant_package_id"
    t.integer "minimum_credit_score"
    t.decimal "minimum_income_rate"
    t.integer "criminal_history"
    t.integer "age_restriction"
    t.integer "eviction_policy", default: 1, null: false
    t.text "financially_responsible_document_requirements"
    t.text "application_disclaimer"
    t.integer "application_lifespan_days", default: 3, null: false
    t.integer "maximum_move_in_days", default: 31, null: false
    t.boolean "display_unavailable_listings", default: false, null: false
    t.bigint "application_deposit_account_id"
    t.bigint "move_in_inspection_template_id"
    t.bigint "move_out_inspection_template_id"
    t.bigint "nsf_charge_preset_id"
    t.bigint "month_to_month_charge_preset_id"
    t.integer "minimum_income_amount_cents"
    t.text "maintenance_blurb"
    t.integer "maintenance_billing_kind", default: 1, null: false
    t.boolean "post_rent_automatically", default: true, null: false
    t.bigint "reservation_charge_preset_id"
    t.integer "screening_preference", default: 0, null: false
    t.boolean "landlord_verification", default: false, null: false
    t.integer "minimum_demand_letter_balance_cents", default: 10000, null: false
    t.integer "application_target_kind", default: 0, null: false
    t.boolean "tours_enabled", default: true, null: false
    t.text "other_adult_occupant_document_requirements"
    t.boolean "financially_responsible_photo_identification_required", default: true, null: false
    t.boolean "financially_responsible_income_proof_required", default: true, null: false
    t.boolean "other_adult_occupant_photo_identification_required", default: true, null: false
    t.boolean "other_adult_occupant_income_proof_required", default: false, null: false
    t.integer "valid_photo_identification_document_kinds", default: [10, 11], null: false, array: true
    t.integer "valid_income_proof_document_kinds", default: [7, 4], null: false, array: true
    t.boolean "vendor_assignment_proof_required", default: false, null: false
    t.bigint "demand_letter_tenant_charge_preset_id"
    t.bigint "demand_letter_owner_charge_preset_id"
    t.boolean "demand_letter_owner_mark_paid", default: false, null: false
    t.boolean "request_owner_work_order_approval", default: false, null: false
    t.boolean "prevent_electronic_payments_while_evicting", default: false, null: false
    t.boolean "income_verification", default: false, null: false
    t.string "saferent_package_identifier"
    t.bigint "eviction_owner_charge_preset_id"
    t.boolean "eviction_owner_mark_paid", default: false, null: false
    t.boolean "mandate_renters_insurance", default: false, null: false
    t.bigint "eviction_tenant_charge_preset_id"
    t.boolean "vendor_insurance_proof_required", default: true, null: false
    t.index ["application_deposit_account_id"], name: "index_configurations_on_application_deposit_account_id"
    t.index ["chart_of_accounts_id"], name: "index_configurations_on_chart_of_accounts_id"
    t.index ["demand_letter_owner_charge_preset_id"], name: "index_configurations_on_demand_letter_owner_charge_preset_id"
    t.index ["demand_letter_tenant_charge_preset_id"], name: "index_configurations_on_demand_letter_tenant_charge_preset_id"
    t.index ["eviction_owner_charge_preset_id"], name: "index_configurations_on_eviction_owner_charge_preset_id"
    t.index ["eviction_tenant_charge_preset_id"], name: "index_configurations_on_eviction_tenant_charge_preset_id"
    t.index ["month_to_month_charge_preset_id"], name: "index_configurations_on_month_to_month_charge_preset_id"
    t.index ["move_in_inspection_template_id"], name: "index_configurations_on_move_in_inspection_template_id"
    t.index ["move_out_inspection_template_id"], name: "index_configurations_on_move_out_inspection_template_id"
    t.index ["nsf_charge_preset_id"], name: "index_configurations_on_nsf_charge_preset_id"
    t.index ["reservation_charge_preset_id"], name: "index_configurations_on_reservation_charge_preset_id"
  end

  create_table "contact_timeline_entries", force: :cascade do |t|
    t.integer "kind", null: false
    t.string "body", null: false
    t.integer "author_id", null: false
    t.integer "regarding_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "unit_id"
    t.string "regarding_type", default: "Tenant", null: false
    t.index ["author_id"], name: "index_contact_timeline_entries_on_author_id"
    t.index ["regarding_type", "regarding_id"], name: "index_contact_timeline_entries_on_regarding"
    t.index ["unit_id"], name: "index_contact_timeline_entries_on_unit_id"
  end

  create_table "credit_cards", force: :cascade do |t|
    t.string "owner_type", null: false
    t.bigint "owner_id", null: false
    t.string "name", null: false
    t.string "last_four", null: false
    t.string "expiration", null: false
    t.string "zeamster_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "merchant_account_id"
    t.string "pay_lease_id"
    t.integer "issuer"
    t.index ["merchant_account_id"], name: "index_credit_cards_on_merchant_account_id"
    t.index ["owner_type", "owner_id"], name: "index_credit_cards_on_owner"
  end

  create_table "credit_presets", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.string "name", null: false
    t.bigint "account_id", null: false
    t.integer "amount_cents", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["account_id"], name: "index_credit_presets_on_account_id"
    t.index ["configuration_id"], name: "index_credit_presets_on_configuration_id"
  end

  create_table "custom_forms_automation_settings_event_date_time_v1s", force: :cascade do |t|
    t.bigint "form_id", null: false
    t.datetime "event_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["form_id"], name: "index_custom_forms_event_date_time_v1s_on_form_id"
  end

  create_table "custom_forms_automation_settings_payment_v1s", force: :cascade do |t|
    t.integer "charge_amount_cents"
    t.bigint "form_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "receivable_account_id", null: false
    t.bigint "bank_account_id", null: false
    t.index ["bank_account_id"], name: "index_custom_forms_payment_v1s_on_bank_account_id"
    t.index ["form_id"], name: "index_custom_forms_automation_settings_payment_v1s_on_form_id"
    t.index ["receivable_account_id"], name: "index_custom_forms_payment_v1s_on_receivable_account_id"
  end

  create_table "custom_forms_element_dates", force: :cascade do |t|
    t.string "label", null: false
    t.string "placeholder", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_dropdowns", force: :cascade do |t|
    t.string "label", null: false
    t.string "placeholder"
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_emails", force: :cascade do |t|
    t.string "label", null: false
    t.string "placeholder", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_messages", force: :cascade do |t|
    t.string "text_color", null: false
    t.string "background_color", null: false
    t.string "content", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_options", force: :cascade do |t|
    t.string "value", null: false
    t.integer "index", null: false
    t.string "optionable_type", null: false
    t.bigint "optionable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["optionable_type", "optionable_id"], name: "index_custom_forms_element_options_on_optionable"
  end

  create_table "custom_forms_element_phone_numbers", force: :cascade do |t|
    t.string "label", null: false
    t.string "placeholder", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_radio_groups", force: :cascade do |t|
    t.string "label", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_section_headers", force: :cascade do |t|
    t.string "content", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_short_texts", force: :cascade do |t|
    t.string "label", null: false
    t.string "placeholder", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_single_check_boxes", force: :cascade do |t|
    t.string "label", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_element_text_areas", force: :cascade do |t|
    t.string "label", null: false
    t.string "placeholder", null: false
    t.boolean "required", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_forms_form_field_answers", force: :cascade do |t|
    t.bigint "submission_id", null: false
    t.bigint "form_field_id", null: false
    t.text "value", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["form_field_id"], name: "index_custom_forms_form_field_answers_on_form_field_id"
    t.index ["submission_id"], name: "index_custom_forms_form_field_answers_on_submission_id"
  end

  create_table "custom_forms_form_fields", force: :cascade do |t|
    t.bigint "form_id", null: false
    t.integer "row", null: false
    t.integer "order", null: false
    t.boolean "removable", null: false
    t.string "element_type"
    t.bigint "element_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "key"
    t.index ["element_type", "element_id"], name: "index_custom_forms_form_fields_on_element"
    t.index ["form_id"], name: "index_custom_forms_form_fields_on_form_id"
  end

  create_table "custom_forms_forms", force: :cascade do |t|
    t.string "name", null: false
    t.string "token", null: false
    t.datetime "archived_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "submissions_count", default: 0, null: false
    t.string "template_name", null: false
    t.date "start_date", null: false
    t.date "end_date"
    t.datetime "published_at"
  end

  create_table "custom_forms_submissions", force: :cascade do |t|
    t.bigint "form_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["form_id"], name: "index_custom_forms_submissions_on_form_id"
  end

  create_table "customer_pricing_plans", force: :cascade do |t|
    t.integer "price_per_unit_cents", default: 0, null: false
    t.integer "flat_rate_cents", default: 0, null: false
    t.integer "billing_scheme", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "monthly_minimum_cents", default: 0, null: false
    t.string "name"
    t.string "description"
    t.string "features"
    t.boolean "public", default: false, null: false
    t.integer "unit_implementation_fee_cents", default: 0, null: false
    t.integer "invoice_processing_fee_cents", default: 0, null: false
    t.integer "invoice_processing_allotted"
    t.integer "ten_ninety_nine_fee_cents", default: 1000, null: false
  end

  create_table "customer_registrations", force: :cascade do |t|
    t.string "uuid", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "email", null: false
    t.string "phone", null: false
    t.string "name"
    t.string "website"
    t.integer "management_style"
    t.integer "unit_count"
    t.string "subdomain"
    t.integer "setup_style"
    t.datetime "submitted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["uuid"], name: "index_customer_registrations_on_uuid", unique: true
  end

  create_table "customers", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.string "subdomain", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "kind"
    t.string "website"
    t.integer "setup_status", default: 0, null: false
    t.integer "representative_id"
    t.integer "billing_representative_id"
    t.string "billing_email"
    t.string "encrypted_twilio_account_sid"
    t.string "encrypted_twilio_account_sid_iv"
    t.string "encrypted_twilio_auth_token"
    t.string "encrypted_twilio_auth_token_iv"
    t.integer "pricing_plan_id"
    t.bigint "enterprise_id"
    t.string "time_zone", default: "Eastern Time (US & Canada)", null: false
    t.datetime "archived_at", precision: nil
    t.string "database_shard", default: "alfa", null: false
    t.integer "primary_countersigner_id"
    t.integer "tunisia_deposit_product"
    t.index ["enterprise_id"], name: "index_customers_on_enterprise_id"
  end

  create_table "data_imports", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.bigint "uploaded_by_id", null: false
    t.json "upload_data"
    t.text "log", default: "", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "import_class_name", null: false
    t.jsonb "options", default: {}, null: false
    t.decimal "percent", default: "0.0", null: false
    t.datetime "archived_at"
    t.index ["uploaded_by_id"], name: "index_data_imports_on_uploaded_by_id"
  end

  create_table "deposit_batches", force: :cascade do |t|
    t.bigint "bank_account_id", null: false
    t.string "name", null: false
    t.date "expected_date", null: false
    t.integer "expected_amount_cents", default: 0, null: false
    t.integer "expected_count", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "posted_at", precision: nil
    t.bigint "reconciliation_id"
    t.index ["bank_account_id"], name: "index_deposit_batches_on_bank_account_id"
    t.index ["reconciliation_id"], name: "index_deposit_batches_on_reconciliation_id"
  end

  create_table "electronic_signatures", force: :cascade do |t|
    t.string "uuid", null: false
    t.bigint "document_id", null: false
    t.string "requested_by_type"
    t.bigint "requested_by_id"
    t.string "recipient_type"
    t.bigint "recipient_id"
    t.boolean "countersigner", default: false, null: false
    t.string "email", null: false
    t.datetime "expires_at", precision: nil, null: false
    t.datetime "signed_at", precision: nil
    t.inet "ip_address"
    t.string "full_name"
    t.text "signature"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "resent_at", precision: nil
    t.string "document_type", null: false
    t.index ["document_id", "document_type"], name: "index_electronic_signatures_on_document_id_and_document_type"
    t.index ["recipient_type", "recipient_id"], name: "index_electronic_signatures_on_recipient"
    t.index ["requested_by_type", "requested_by_id"], name: "index_electronic_signatures_on_requested_by"
    t.index ["uuid"], name: "index_electronic_signatures_on_uuid", unique: true
  end

  create_table "email_receipts", force: :cascade do |t|
    t.string "token", null: false
    t.string "mailer", null: false
    t.string "action", null: false
    t.string "subject"
    t.string "to", default: [], null: false, array: true
    t.string "cc", default: [], array: true
    t.string "bcc", default: [], array: true
    t.string "from", null: false
    t.string "reply_to"
    t.integer "status", default: 0, null: false
    t.integer "open_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["token"], name: "index_email_receipts_on_token"
  end

  create_table "enterprises", force: :cascade do |t|
    t.string "name", null: false
    t.string "subdomain_prefix", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_enterprises_on_name", unique: true
    t.index ["subdomain_prefix"], name: "index_enterprises_on_subdomain_prefix", unique: true
  end

  create_table "events", id: :serial, force: :cascade do |t|
    t.string "topic", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "financing_refinance_invites", force: :cascade do |t|
    t.string "token", null: false
    t.bigint "property_id", null: false
    t.bigint "owner_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_financing_refinance_invites_on_created_by_id"
    t.index ["owner_id"], name: "index_financing_refinance_invites_on_owner_id"
    t.index ["property_id"], name: "index_financing_refinance_invites_on_property_id"
  end

  create_table "financing_refinance_requests", force: :cascade do |t|
    t.bigint "invite_id", null: false
    t.integer "deed_type", null: false
    t.string "deed_name", null: false
    t.date "purchase_date", null: false
    t.integer "lein_type", null: false
    t.decimal "lein_interest_rate"
    t.date "entity_founded_date"
    t.integer "entity_outstanding_debt_cents"
    t.integer "entity_monthly_debt_service_cents"
    t.integer "employment_status", null: false
    t.integer "estimated_fico_score", null: false
    t.integer "average_monthly_income_cents", default: 0, null: false
    t.integer "average_monthly_debt_obligation_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["invite_id"], name: "index_financing_refinance_requests_on_invite_id"
  end

  create_table "floorplans", id: :serial, force: :cascade do |t|
    t.integer "property_id", null: false
    t.string "name", null: false
    t.integer "square_feet", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "bedrooms", default: 0, null: false
    t.float "bathrooms", default: 0.0, null: false
    t.integer "price_cents", default: 0, null: false
    t.string "image_file_name"
    t.string "image_content_type"
    t.bigint "image_file_size"
    t.datetime "image_updated_at", precision: nil
    t.index ["property_id"], name: "index_floorplans_on_property_id"
  end

  create_table "guest_cards", id: :serial, force: :cascade do |t|
    t.integer "tenant_id", null: false
    t.integer "property_id"
    t.jsonb "data", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "floorplan_id"
    t.date "move_in_date"
    t.integer "external_source_id"
    t.integer "internal_source_id"
    t.datetime "archived_at", precision: nil
    t.datetime "requested_tour_times", precision: nil, array: true
    t.integer "style"
    t.integer "preferred_contact_method"
    t.bigint "portfolio_id"
    t.date "move_out_date"
    t.index ["portfolio_id"], name: "index_guest_cards_on_portfolio_id"
  end

  create_table "hap_contracts", force: :cascade do |t|
    t.bigint "property_id", null: false
    t.string "public_housing_authority", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.integer "subsidy_type", default: 1, null: false
    t.string "project_name", null: false
    t.string "irems_property_id"
    t.string "project_number"
    t.string "contract_number"
    t.string "imax_id"
    t.integer "plan_of_action"
    t.boolean "hud_owned", default: false, null: false
    t.integer "fips_county_code"
    t.integer "contract_unit_count", default: 0, null: false
    t.integer "disabled_unit_count", default: 0, null: false
    t.integer "elderly_unit_count", default: 0, null: false
    t.integer "elderly_or_disabled_unit_count", default: 0, null: false
    t.integer "supportive_services_unit_count", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["property_id"], name: "index_hap_contracts_on_property_id"
  end

  create_table "income_certifications", force: :cascade do |t|
    t.integer "lease_application_id"
    t.string "code"
    t.date "effective_date", null: false
    t.integer "subsidy_type", default: 0, null: false
    t.integer "correction_type", default: 0, null: false
    t.integer "correction_id"
    t.integer "previous_unit_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "lease_id"
  end

  create_table "inspectify_orders", force: :cascade do |t|
    t.bigint "inspection_report_id", null: false
    t.string "inspectify_order_id"
    t.integer "inspectify_status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "completed_at"
    t.index ["inspectify_order_id"], name: "index_inspectify_orders_on_inspectify_order_id", unique: true
    t.index ["inspection_report_id"], name: "index_inspectify_orders_on_inspection_report_id"
    t.index ["inspection_report_id"], name: "index_unique_active_inspectify_order_per_report", unique: true, where: "(inspectify_status <> ALL (ARRAY[1, 5, 6]))"
  end

  create_table "inspectify_vendor_tokens", force: :cascade do |t|
    t.bigint "vendor_id", null: false
    t.string "encrypted_api_token", null: false
    t.string "encrypted_api_token_iv", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "external_team_id"
    t.string "internal_team_id"
    t.index ["vendor_id"], name: "index_inspectify_vendor_tokens_on_vendor_id"
  end

  create_table "inspection_activities", force: :cascade do |t|
    t.bigint "inspection_report_id", null: false
    t.string "actor_type"
    t.bigint "actor_id"
    t.integer "kind", null: false
    t.string "assigned_to_type"
    t.bigint "assigned_to_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["actor_type", "actor_id"], name: "index_inspection_activities_on_actor"
    t.index ["assigned_to_type", "assigned_to_id"], name: "index_inspection_activities_on_assigned_to"
    t.index ["inspection_report_id"], name: "index_inspection_activities_on_inspection_report_id"
  end

  create_table "inspection_punch_list_entries", force: :cascade do |t|
    t.bigint "response_id", null: false
    t.bigint "sku_list_item_id", null: false
    t.integer "count", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["response_id"], name: "index_inspection_punch_list_entries_on_response_id"
    t.index ["sku_list_item_id"], name: "index_inspection_punch_list_entries_on_sku_list_item_id"
  end

  create_table "inspection_questions", force: :cascade do |t|
    t.bigint "template_id", null: false
    t.integer "category", null: false
    t.string "section"
    t.integer "kind", null: false
    t.string "prompt", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "options", array: true
    t.integer "room_kind"
    t.string "inspectify_id"
    t.string "reams_id"
    t.string "sierra_leone_source"
    t.index ["inspectify_id"], name: "index_inspection_questions_on_inspectify_id", unique: true
    t.index ["reams_id"], name: "index_inspection_questions_on_reams_id", unique: true
    t.index ["sierra_leone_source"], name: "index_inspection_questions_on_sierra_leone_source"
    t.index ["template_id"], name: "index_inspection_questions_on_template_id"
  end

  create_table "inspection_records", force: :cascade do |t|
    t.bigint "report_id", null: false
    t.bigint "parent_id"
    t.string "target_type", null: false
    t.bigint "target_id", null: false
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "comments"
    t.index ["parent_id"], name: "index_inspection_records_on_parent_id"
    t.index ["report_id"], name: "index_inspection_records_on_report_id"
    t.index ["target_type", "target_id"], name: "index_inspection_records_on_target"
  end

  create_table "inspection_reports", force: :cascade do |t|
    t.string "name", null: false
    t.integer "kind", null: false
    t.bigint "opened_by_id"
    t.datetime "completed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "assigned_to_type"
    t.bigint "assigned_to_id"
    t.bigint "template_id"
    t.string "comments"
    t.text "signature"
    t.string "target_type", null: false
    t.bigint "target_id", null: false
    t.bigint "property_id", null: false
    t.bigint "task_id"
    t.date "due_date"
    t.string "entry_code"
    t.text "message"
    t.integer "status", default: 0, null: false
    t.date "earliest_start_date"
    t.datetime "archived_at"
    t.datetime "submitted_at"
    t.index ["assigned_to_type", "assigned_to_id"], name: "index_inspection_reports_on_assigned_to_type_and_assigned_to_id"
    t.index ["opened_by_id"], name: "index_inspection_reports_on_opened_by_id"
    t.index ["property_id"], name: "index_inspection_reports_on_property_id"
    t.index ["status"], name: "index_inspection_reports_on_status"
    t.index ["target_type", "target_id"], name: "index_inspection_reports_on_target_type_and_target_id"
    t.index ["task_id"], name: "index_inspection_reports_on_task_id"
    t.index ["template_id"], name: "index_inspection_reports_on_template_id"
  end

  create_table "inspection_responses", force: :cascade do |t|
    t.bigint "question_id", null: false
    t.string "body"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "record_id", null: false
    t.string "note"
    t.integer "room_index", default: 0, null: false
    t.bigint "room_id"
    t.index ["question_id"], name: "index_inspection_responses_on_question_id"
    t.index ["record_id"], name: "index_inspection_responses_on_record_id"
    t.index ["room_id"], name: "index_inspection_responses_on_room_id"
  end

  create_table "inspection_templates", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "archived_at", precision: nil
  end

  create_table "insurance_policies", force: :cascade do |t|
    t.string "provider", null: false
    t.string "policy_number", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "holder_type", null: false
    t.bigint "holder_id", null: false
    t.string "agent_first_name"
    t.string "agent_last_name"
    t.string "agent_email"
    t.string "agent_phone"
    t.integer "coverage_amount_cents"
    t.bigint "lease_id"
    t.index ["holder_type", "holder_id"], name: "index_insurance_policies_on_holder"
    t.index ["lease_id"], name: "index_insurance_policies_on_lease_id"
  end

  create_table "insurance_requirements", force: :cascade do |t|
    t.integer "kind", default: 0, null: false
    t.string "scope_type", null: false
    t.bigint "scope_id", null: false
    t.date "start_date"
    t.date "end_date"
    t.integer "minimum_amount_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["scope_type", "scope_id"], name: "index_insurance_requirements_on_scope"
  end

  create_table "invoice_payments", id: :serial, force: :cascade do |t|
    t.integer "payment_id", null: false
    t.integer "invoice_id", null: false
    t.integer "amount_cents", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "reversed_at"
    t.date "date", null: false
    t.index ["invoice_id"], name: "index_invoice_payments_on_invoice_id"
    t.index ["payment_id"], name: "index_invoice_payments_on_payment_id"
  end

  create_table "invoice_processing_associated_attachments", force: :cascade do |t|
    t.bigint "invoice_id", null: false
    t.bigint "attachment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attachment_id"], name: "index_invoice_processing_associated_attachments_on_attachments"
    t.index ["invoice_id"], name: "index_invoice_processing_associated_attachments_on_invoice_id"
  end

  create_table "invoice_processing_emails", id: :serial, force: :cascade do |t|
    t.string "to", null: false
    t.string "from", null: false
    t.string "subject"
    t.string "body"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "processed_at", precision: nil
    t.bigint "assigned_to_id", null: false
    t.datetime "assigned_at", precision: nil, null: false
    t.bigint "processed_by_id"
    t.integer "status", default: 0, null: false
    t.string "rejected_reason"
    t.datetime "notification_sent_at", precision: nil
    t.bigint "customer_id", null: false
    t.index ["assigned_to_id"], name: "index_invoice_processing_emails_on_assigned_to_id"
    t.index ["customer_id"], name: "index_invoice_processing_emails_on_customer_id"
    t.index ["processed_by_id"], name: "index_invoice_processing_emails_on_processed_by_id"
  end

  create_table "invoice_processing_users", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "invoices", id: :serial, force: :cascade do |t|
    t.string "buyer_type", null: false
    t.integer "buyer_id", null: false
    t.string "seller_type", null: false
    t.integer "seller_id", null: false
    t.string "description", null: false
    t.string "invoice_number"
    t.date "post_date", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "due_date", default: -> { "(CURRENT_DATE + 30)" }, null: false
    t.date "waived_at"
    t.bigint "payment_batch_id"
    t.text "note"
    t.datetime "notification_sent_at", precision: nil
    t.datetime "invoice_payment_approved_at", precision: nil
    t.integer "payment_batch_order", default: 0, null: false
    t.boolean "owner_visible", default: false, null: false
    t.bigint "forwarded_from_id"
    t.date "physical_date", null: false
    t.integer "amount_cents", null: false
    t.bigint "buyer_unit_id"
    t.bigint "seller_unit_id"
    t.bigint "buyer_lease_membership_id"
    t.bigint "seller_lease_membership_id"
    t.bigint "invoice_processing_email_id"
    t.bigint "recurring_schedule_id"
    t.bigint "loan_id"
    t.integer "notification_preference", default: 0, null: false
    t.boolean "payment_plan_eligible", default: false, null: false
    t.index ["buyer_lease_membership_id"], name: "index_invoices_on_buyer_lease_membership_id"
    t.index ["buyer_type", "buyer_id"], name: "index_invoices_on_buyer"
    t.index ["buyer_unit_id"], name: "index_invoices_on_buyer_unit_id"
    t.index ["forwarded_from_id"], name: "index_invoices_on_forwarded_from_id"
    t.index ["invoice_processing_email_id"], name: "index_invoices_on_invoice_processing_email_id"
    t.index ["loan_id"], name: "index_invoices_on_loan_id"
    t.index ["payment_batch_id"], name: "index_invoices_on_payment_batch_id"
    t.index ["post_date"], name: "index_invoices_on_post_date"
    t.index ["recurring_schedule_id"], name: "index_invoices_on_recurring_schedule_id"
    t.index ["seller_lease_membership_id"], name: "index_invoices_on_seller_lease_membership_id"
    t.index ["seller_type", "seller_id"], name: "index_invoices_on_seller"
    t.index ["seller_unit_id"], name: "index_invoices_on_seller_unit_id"
  end

  create_table "itemized_damages", force: :cascade do |t|
    t.bigint "lease_membership_id", null: false
    t.date "walk_through_date"
    t.integer "remaining_deposit_cents", default: 0, null: false
    t.integer "other_credit_cents", default: 0, null: false
    t.integer "arrearage_cents", default: 0, null: false
    t.integer "damage_ids", default: [], null: false, array: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "move_out_id", null: false
    t.string "accounting_documents_gids", default: [], null: false, array: true
    t.index ["lease_membership_id"], name: "index_itemized_damages_on_lease_membership_id"
    t.index ["move_out_id"], name: "index_itemized_damages_on_move_out_id"
  end

  create_table "job_postings", force: :cascade do |t|
    t.integer "kind", default: 0, null: false
    t.string "category", null: false
    t.string "title", null: false
    t.text "description", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "uuid", null: false
  end

  create_table "labor_items", force: :cascade do |t|
    t.date "date"
    t.boolean "skip", default: false, null: false
    t.bigint "employee_id"
    t.decimal "hours"
    t.integer "rate_cents", default: 0, null: false
    t.string "rate_currency", default: "USD", null: false
    t.bigint "maintenance_ticket_id"
    t.bigint "line_item_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["employee_id"], name: "index_labor_items_on_employee_id"
    t.index ["line_item_id"], name: "index_labor_items_on_line_item_id"
    t.index ["maintenance_ticket_id"], name: "index_labor_items_on_maintenance_ticket_id"
  end

  create_table "landlord_verification_landlord_informations", force: :cascade do |t|
    t.string "landlord_name", null: false
    t.string "landlord_email", null: false
    t.string "landlord_phone", null: false
    t.bigint "landlord_verification_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["landlord_verification_id"], name: "index_landlord_informations_on_landlord_verification_id"
  end

  create_table "landlord_verification_lease_informations", force: :cascade do |t|
    t.date "move_in_date", null: false
    t.date "move_out_date", null: false
    t.integer "monthly_rent_cents", null: false
    t.integer "late_rent_count", null: false
    t.integer "move_out_condition", null: false
    t.integer "number_of_occupants", null: false
    t.boolean "notice_given", null: false
    t.boolean "taken_to_court", null: false
    t.text "return_full_security_deposit"
    t.text "additional_information"
    t.bigint "landlord_verification_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["landlord_verification_id"], name: "index_lease_informations_on_landlord_verification_id"
  end

  create_table "landlord_verification_tenant_informations", force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.bigint "landlord_verification_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["landlord_verification_id"], name: "index_tenant_informations_on_landlord_verification_id"
  end

  create_table "landlord_verifications", force: :cascade do |t|
    t.string "uuid", null: false
    t.bigint "lease_application_residence_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "submitted_at", precision: nil
    t.datetime "skipped_at", precision: nil
    t.index ["lease_application_residence_id"], name: "index_landlord_verifications_on_lease_application_residence_id"
  end

  create_table "lease_addendums", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.string "name", null: false
    t.date "start_date"
    t.date "end_date"
    t.datetime "executed_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lease_id"], name: "index_lease_addendums_on_lease_id"
  end

  create_table "lease_application_contacts", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.string "type", default: "LeaseApplication::EmergencyContact", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone", null: false
    t.string "relation", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "email"
    t.index ["lease_application_id"], name: "index_lease_application_contacts_on_lease_application_id"
  end

  create_table "lease_application_credit_references", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.integer "applicant_id", null: false
    t.string "name", null: false
    t.string "phone"
    t.integer "kind", default: 0, null: false
    t.integer "balance_cents", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["lease_application_id"], name: "index_credit_references_on_lease_applications"
  end

  create_table "lease_application_custom_question_responses", force: :cascade do |t|
    t.bigint "custom_question_id", null: false
    t.bigint "lease_application_id", null: false
    t.text "text", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["custom_question_id"], name: "response_on_custom_question_id"
    t.index ["lease_application_id"], name: "response_on_lease_application_id"
  end

  create_table "lease_application_custom_questions", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.text "text", null: false
    t.datetime "archived_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["configuration_id"], name: "index_lease_application_custom_questions_on_configuration_id"
  end

  create_table "lease_application_income_sources", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.integer "applicant_id", null: false
    t.string "type", null: false
    t.string "name", null: false
    t.string "supervisor"
    t.string "phone"
    t.string "occupation"
    t.date "start_date"
    t.date "end_date"
    t.integer "monthly_income_cents", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["lease_application_id"], name: "index_lease_application_income_sources_on_lease_application_id"
  end

  create_table "lease_application_memberships", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "primary", default: false, null: false
    t.index ["lease_application_id"], name: "index_lease_application_memberships_on_lease_application_id"
    t.index ["tenant_id"], name: "index_lease_application_memberships_on_tenant_id"
  end

  create_table "lease_application_residences", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.integer "applicant_id", null: false
    t.string "name", null: false
    t.integer "monthly_amount_cents", default: 0
    t.string "reason_for_moving"
    t.string "contact"
    t.string "email"
    t.string "phone"
    t.date "start_date"
    t.date "end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "rental", default: false, null: false
    t.index ["lease_application_id"], name: "index_lease_application_residences_on_lease_application_id"
  end

  create_table "lease_applications", id: :serial, force: :cascade do |t|
    t.integer "property_id", null: false
    t.jsonb "encrypted_form", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at", precision: nil
    t.string "encrypted_form_iv"
    t.datetime "archived_at", precision: nil
    t.boolean "screening_initiated", default: false, null: false
    t.string "email"
    t.string "uuid", null: false
    t.datetime "expires_at", precision: nil, null: false
    t.datetime "submitted_at", precision: nil
    t.bigint "floorplan_id", null: false
    t.bigint "unit_id"
    t.bigint "lead_id"
    t.date "move_in_date"
    t.bigint "lease_term_id"
    t.datetime "approved_at", precision: nil
    t.datetime "rejected_at", precision: nil
    t.bigint "adjudicated_by_id"
    t.boolean "payment_received", default: false, null: false
    t.datetime "reviewed_at", precision: nil
    t.datetime "reminder_sent_at", precision: nil
    t.decimal "scorecard_score", default: "0.0", null: false
    t.integer "computed_household_income_cents"
    t.integer "computed_reported_household_income_cents"
    t.index ["adjudicated_by_id"], name: "index_lease_applications_on_adjudicated_by_id"
    t.index ["floorplan_id"], name: "index_lease_applications_on_floorplan_id"
    t.index ["lead_id"], name: "index_lease_applications_on_lead_id"
    t.index ["lease_term_id"], name: "index_lease_applications_on_lease_term_id"
    t.index ["unit_id"], name: "index_lease_applications_on_unit_id"
  end

  create_table "lease_memberships", id: :serial, force: :cascade do |t|
    t.integer "lease_id"
    t.integer "tenant_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "move_in_date", null: false
    t.date "move_out_date", null: false
    t.boolean "move_out_processed", default: false, null: false
    t.integer "role", default: 0, null: false
    t.index ["lease_id"], name: "index_lease_memberships_on_lease_id"
    t.index ["role"], name: "index_lease_memberships_on_role"
    t.index ["tenant_id"], name: "index_lease_memberships_on_tenant_id"
  end

  create_table "lease_move_out_custom_damages", force: :cascade do |t|
    t.bigint "move_out_id", null: false
    t.string "name", null: false
    t.bigint "account_id", null: false
    t.integer "amount_cents", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["account_id"], name: "index_lease_move_out_custom_damages_on_account_id"
    t.index ["move_out_id"], name: "index_lease_move_out_custom_damages_on_move_out_id"
  end

  create_table "lease_move_out_memberships", force: :cascade do |t|
    t.bigint "move_out_id", null: false
    t.bigint "lease_membership_id", null: false
    t.index ["lease_membership_id"], name: "index_lease_move_out_memberships_on_lease_membership_id"
    t.index ["move_out_id"], name: "index_lease_move_out_memberships_on_move_out_id"
  end

  create_table "lease_move_outs", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.date "move_out_date"
    t.date "return_of_possession_date"
    t.date "walk_through_date"
    t.text "walk_through_summary"
    t.integer "charge_preset_ids", default: [], null: false, array: true
    t.datetime "processed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "termination_date"
    t.integer "termination_reason"
    t.string "termination_description"
    t.boolean "terminate_lease", default: false, null: false
    t.bigint "rekey_maintenance_ticket_id"
    t.bigint "inspection_id"
    t.integer "accounting_date_setting", default: 0, null: false
    t.date "explicit_accounting_date"
    t.index ["inspection_id"], name: "index_lease_move_outs_on_inspection_id"
    t.index ["lease_id"], name: "index_lease_move_outs_on_lease_id"
    t.index ["rekey_maintenance_ticket_id"], name: "index_lease_move_outs_on_rekey_maintenance_ticket_id"
  end

  create_table "lease_notice_of_non_renewals", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.string "submitted_by_type", null: false
    t.bigint "submitted_by_id", null: false
    t.date "date", null: false
    t.date "anticipated_move_out_date", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "reason", null: false
    t.index ["lease_id"], name: "index_lease_notice_of_non_renewals_on_lease_id"
    t.index ["submitted_by_type", "submitted_by_id"], name: "index_lease_notice_of_non_renewals_on_submitted_by"
  end

  create_table "lease_pet_memberships", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.bigint "pet_id", null: false
    t.date "move_in_date"
    t.date "move_out_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lease_id", "pet_id"], name: "index_lease_pet_memberships_on_lease_id_and_pet_id", unique: true
    t.index ["lease_id"], name: "index_lease_pet_memberships_on_lease_id"
    t.index ["pet_id"], name: "index_lease_pet_memberships_on_pet_id"
  end

  create_table "lease_terms", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.string "name", null: false
    t.integer "length", null: false
    t.boolean "renewal_only", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["configuration_id"], name: "index_lease_terms_on_configuration_id"
  end

  create_table "leases", id: :serial, force: :cascade do |t|
    t.integer "unit_id", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "kind", default: 0, null: false
    t.bigint "renewal_id"
    t.datetime "archived_at", precision: nil
    t.integer "termination_reason"
    t.string "termination_description"
    t.date "end_date_before_termination"
    t.datetime "executed_at", precision: nil
    t.integer "grace_period_days"
    t.boolean "eligible_for_placement_fee", default: true, null: false
    t.date "end_date_before_rollover"
    t.index ["renewal_id"], name: "index_leases_on_renewal_id"
    t.index ["unit_id"], name: "index_leases_on_unit_id"
  end

  create_table "lending_loan_installment_adjustments", force: :cascade do |t|
    t.bigint "loan_id", null: false
    t.date "effective_date", null: false
    t.integer "explicit_installment_amount_cents", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["loan_id"], name: "index_lending_loan_installment_adjustments_on_loan_id"
  end

  create_table "lending_loans", force: :cascade do |t|
    t.bigint "lender_id", null: false
    t.bigint "borrower_id", null: false
    t.bigint "property_id", null: false
    t.string "loan_number", null: false
    t.date "origination_date", null: false
    t.date "first_payment_date", null: false
    t.date "maturity_date", null: false
    t.decimal "interest_rate", null: false
    t.bigint "principal_cents", default: 0, null: false
    t.string "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "repayment_type", default: 0, null: false
    t.datetime "archived_at", precision: nil
    t.date "interest_start_date", null: false
    t.integer "payment_frequency", default: 0, null: false
    t.integer "explicit_installment_amount_cents"
    t.index ["borrower_id"], name: "index_lending_loans_on_borrower_id"
    t.index ["lender_id"], name: "index_lending_loans_on_lender_id"
    t.index ["property_id"], name: "index_lending_loans_on_property_id"
  end

  create_table "line_item_markups", force: :cascade do |t|
    t.bigint "source_item_id", null: false
    t.bigint "destination_item_id"
    t.boolean "skip", default: false, null: false
    t.integer "flat_markup_cents"
    t.string "flat_markup_currency", default: "USD", null: false
    t.decimal "percentage_markup"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["destination_item_id"], name: "index_line_item_markups_on_destination_item_id"
    t.index ["source_item_id"], name: "index_line_item_markups_on_source_item_id"
  end

  create_table "line_items", id: :serial, force: :cascade do |t|
    t.integer "invoice_id", null: false
    t.string "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "quantity", default: 1, null: false
    t.integer "unit_price_cents", default: 0, null: false
    t.bigint "payable_account_id"
    t.bigint "receivable_account_id"
    t.bigint "charge_preset_id"
    t.index ["charge_preset_id"], name: "index_line_items_on_charge_preset_id"
    t.index ["invoice_id"], name: "index_line_items_on_invoice_id"
    t.index ["payable_account_id"], name: "index_line_items_on_payable_account_id"
    t.index ["receivable_account_id"], name: "index_line_items_on_receivable_account_id"
  end

  create_table "linkages", force: :cascade do |t|
    t.bigint "invoice_id"
    t.bigint "project_id"
    t.bigint "maintenance_ticket_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["invoice_id"], name: "index_linkages_on_invoice_id"
    t.index ["maintenance_ticket_id"], name: "index_linkages_on_maintenance_ticket_id"
    t.index ["project_id"], name: "index_linkages_on_project_id"
  end

  create_table "listing_publish_events", force: :cascade do |t|
    t.bigint "listing_id", null: false
    t.datetime "timestamp", precision: nil, default: -> { "now()" }, null: false
    t.integer "direction", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_listing_publish_events_on_listing_id"
  end

  create_table "listings", id: :serial, force: :cascade do |t|
    t.string "description", null: false
    t.string "amenities", default: [], null: false, array: true
    t.boolean "published", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "floorplan_id"
    t.bigint "agent_id"
    t.string "name", null: false
    t.string "virtual_tour_url"
    t.string "notes"
    t.string "schedule_tour_url"
    t.boolean "zillow_feed", default: false, null: false
    t.index ["agent_id"], name: "index_listings_on_agent_id"
    t.index ["floorplan_id"], name: "index_listings_on_floorplan_id"
  end

  create_table "maintenance_bid_items", force: :cascade do |t|
    t.bigint "bid_id", null: false
    t.string "description"
    t.integer "materials_amount_cents", default: 0, null: false
    t.integer "labor_amount_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["bid_id"], name: "index_maintenance_bid_items_on_bid_id"
  end

  create_table "maintenance_bid_request_invites", force: :cascade do |t|
    t.bigint "request_id", null: false
    t.bigint "vendor_id", null: false
    t.boolean "declined", default: false, null: false
    t.text "declined_reason"
    t.string "token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["request_id"], name: "index_maintenance_bid_request_invites_on_request_id"
    t.index ["vendor_id"], name: "index_maintenance_bid_request_invites_on_vendor_id"
  end

  create_table "maintenance_bid_requests", force: :cascade do |t|
    t.string "token", null: false
    t.bigint "maintenance_ticket_id", null: false
    t.bigint "vendor_id"
    t.bigint "requested_by_id", null: false
    t.string "message"
    t.boolean "declined", default: false, null: false
    t.string "declined_reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "include_attachments", default: false, null: false
    t.index ["maintenance_ticket_id"], name: "index_maintenance_bid_requests_on_maintenance_ticket_id"
    t.index ["requested_by_id"], name: "index_maintenance_bid_requests_on_requested_by_id"
    t.index ["token"], name: "index_maintenance_bid_requests_on_token", unique: true
    t.index ["vendor_id"], name: "index_maintenance_bid_requests_on_vendor_id"
  end

  create_table "maintenance_bids", force: :cascade do |t|
    t.bigint "request_id"
    t.string "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "invite_id"
    t.index ["request_id"], name: "index_maintenance_bids_on_request_id"
    t.check_constraint "invite_id IS NOT NULL", name: "check_invite_id_not_null"
  end

  create_table "maintenance_estimate_areas", force: :cascade do |t|
    t.bigint "section_id", null: false
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["section_id"], name: "index_maintenance_estimate_areas_on_section_id"
  end

  create_table "maintenance_estimate_sections", force: :cascade do |t|
    t.bigint "estimate_id", null: false
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["estimate_id"], name: "index_maintenance_estimate_sections_on_estimate_id"
  end

  create_table "maintenance_estimate_tasks", force: :cascade do |t|
    t.bigint "record_id"
    t.bigint "question_id"
    t.integer "room_index", default: 0, null: false
    t.string "body", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "area_id"
    t.integer "materials_amount_cents", default: 0, null: false
    t.integer "labor_amount_cents", default: 0, null: false
    t.index ["area_id"], name: "index_maintenance_estimate_tasks_on_area_id"
    t.index ["question_id"], name: "index_maintenance_estimate_tasks_on_question_id"
    t.index ["record_id"], name: "index_maintenance_estimate_tasks_on_record_id"
  end

  create_table "maintenance_estimates", force: :cascade do |t|
    t.bigint "bill_to_id"
    t.bigint "bill_from_id"
    t.bigint "inspection_id"
    t.bigint "prepared_by_id"
    t.string "summary"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "extra_materials_amount_cents", default: 0, null: false
    t.integer "extra_labor_amount_cents", default: 0, null: false
    t.bigint "maintenance_ticket_id"
    t.bigint "property_id", null: false
    t.bigint "unit_id"
    t.boolean "display_item_amounts", default: true, null: false
    t.boolean "include_estimable_photos", default: true, null: false
    t.string "materials_markup_kind", default: "percent", null: false
    t.decimal "materials_markup_raw", default: "0.0", null: false
    t.string "labor_markup_kind", default: "percent", null: false
    t.decimal "labor_markup_raw", default: "0.0", null: false
    t.bigint "copied_from_id"
    t.boolean "copy_photos"
    t.index ["bill_from_id"], name: "index_maintenance_estimates_on_bill_from_id"
    t.index ["bill_to_id"], name: "index_maintenance_estimates_on_bill_to_id"
    t.index ["copied_from_id"], name: "index_maintenance_estimates_on_copied_from_id"
    t.index ["inspection_id"], name: "index_maintenance_estimates_on_inspection_id"
    t.index ["maintenance_ticket_id"], name: "index_maintenance_estimates_on_maintenance_ticket_id"
    t.index ["prepared_by_id"], name: "index_maintenance_estimates_on_prepared_by_id"
    t.index ["property_id"], name: "index_maintenance_estimates_on_property_id"
    t.index ["unit_id"], name: "index_maintenance_estimates_on_unit_id"
  end

  create_table "maintenance_surveys", force: :cascade do |t|
    t.integer "experience_rating"
    t.integer "quality_rating"
    t.text "feedback"
    t.bigint "maintenance_ticket_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "submitted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["maintenance_ticket_id"], name: "index_maintenance_surveys_on_maintenance_ticket_id"
    t.index ["tenant_id"], name: "index_maintenance_surveys_on_tenant_id"
  end

  create_table "maintenance_ticket_appointments", force: :cascade do |t|
    t.bigint "maintenance_ticket_id", null: false
    t.bigint "created_by_id"
    t.datetime "scheduled_for", precision: nil, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "vendor_id"
    t.bigint "employee_id"
    t.boolean "notify_tenant", default: false, null: false
    t.boolean "notify_owner", default: false, null: false
    t.interval "window"
    t.index ["created_by_id"], name: "index_maintenance_ticket_appointments_on_created_by_id"
    t.index ["employee_id"], name: "index_maintenance_ticket_appointments_on_employee_id"
    t.index ["maintenance_ticket_id"], name: "index_maintenance_ticket_appointments_on_maintenance_ticket_id"
    t.index ["vendor_id"], name: "index_maintenance_ticket_appointments_on_vendor_id"
  end

  create_table "maintenance_ticket_defers", force: :cascade do |t|
    t.bigint "maintenance_ticket_id", null: false
    t.bigint "created_by_id"
    t.string "reason"
    t.datetime "defer_until", precision: nil, null: false
    t.integer "after_expire", default: 0, null: false
    t.datetime "evaluated_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_maintenance_ticket_defers_on_created_by_id"
    t.index ["maintenance_ticket_id"], name: "index_maintenance_ticket_defers_on_maintenance_ticket_id"
  end

  create_table "maintenance_ticket_events", force: :cascade do |t|
    t.bigint "maintenance_ticket_id", null: false
    t.bigint "author_id", null: false
    t.integer "kind", null: false
    t.text "body"
    t.bigint "employee_assignee_id"
    t.bigint "vendor_assignee_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "invoice_id"
    t.bigint "estimate_id"
    t.string "author_type", null: false
    t.integer "visibility", default: 0, null: false
    t.bigint "bid_id"
    t.index ["author_id", "author_type"], name: "index_maintenance_ticket_events_on_author_id_and_author_type"
    t.index ["bid_id"], name: "index_maintenance_ticket_events_on_bid_id"
    t.index ["employee_assignee_id"], name: "index_maintenance_ticket_events_on_employee_assignee_id"
    t.index ["estimate_id"], name: "index_maintenance_ticket_events_on_estimate_id"
    t.index ["invoice_id"], name: "index_maintenance_ticket_events_on_invoice_id"
    t.index ["maintenance_ticket_id"], name: "index_maintenance_ticket_events_on_maintenance_ticket_id"
    t.index ["vendor_assignee_id"], name: "index_maintenance_ticket_events_on_vendor_assignee_id"
  end

  create_table "maintenance_tickets", id: :serial, force: :cascade do |t|
    t.string "description", default: "", null: false
    t.integer "tenant_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "closed_at", precision: nil
    t.integer "property_id"
    t.string "subject"
    t.integer "status", default: 0, null: false
    t.integer "opened_by_id"
    t.string "opened_by_type"
    t.boolean "template", default: false, null: false
    t.string "schedule"
    t.integer "parent_id"
    t.integer "unit_id"
    t.integer "lease_id"
    t.integer "urgency", default: 1, null: false
    t.datetime "opened_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "assessed_at", precision: nil
    t.bigint "assessed_by_id"
    t.boolean "permission_to_enter", default: true, null: false
    t.datetime "requested_appointment_times", precision: nil, array: true
    t.string "number"
    t.integer "billing_rate_cents", default: 0, null: false
    t.decimal "materials_markup_rate", default: "0.0", null: false
    t.string "entry_code"
    t.index ["assessed_by_id"], name: "index_maintenance_tickets_on_assessed_by_id"
    t.index ["lease_id"], name: "index_maintenance_tickets_on_lease_id"
    t.index ["status"], name: "index_maintenance_tickets_on_status"
    t.index ["unit_id"], name: "index_maintenance_tickets_on_unit_id"
  end

  create_table "maintenance_work_performed_statement_items", force: :cascade do |t|
    t.bigint "statement_id", null: false
    t.bigint "maintenance_ticket_id", null: false
    t.string "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "labor_time"
    t.index ["maintenance_ticket_id"], name: "index_maintenance_statement_items_on_maintenance_tickets"
    t.index ["statement_id"], name: "index_maintenance_statement_items_on_statement"
  end

  create_table "management_contract_account_settings", force: :cascade do |t|
    t.bigint "management_contract_id", null: false
    t.bigint "account_id", null: false
    t.decimal "ratio", default: "1.0", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_management_contract_account_settings_on_account_id"
    t.index ["management_contract_id"], name: "index_account_settings_on_management_contract_id"
  end

  create_table "management_contract_memberships", force: :cascade do |t|
    t.bigint "management_contract_id", null: false
    t.bigint "property_id", null: false
    t.date "start_date"
    t.date "end_date"
    t.integer "reserve_amount_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["management_contract_id"], name: "index_management_contract_memberships_on_management_contract_id"
    t.index ["property_id"], name: "index_management_contract_memberships_on_property_id"
  end

  create_table "management_contracts", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.decimal "rent_markup_raw"
    t.string "rent_markup_kind"
    t.decimal "parking_markup_raw"
    t.string "parking_markup_kind"
    t.decimal "late_markup_raw"
    t.string "late_markup_kind"
    t.decimal "new_lease_markup_raw"
    t.string "new_lease_markup_kind"
    t.decimal "renewal_markup_raw"
    t.string "renewal_markup_kind"
    t.decimal "labor_markup_raw"
    t.string "labor_markup_kind"
    t.decimal "material_markup_raw"
    t.string "material_markup_kind"
    t.integer "maintenance_limit_cents"
    t.integer "payables_limit_cents"
    t.datetime "executed_at", precision: nil
    t.date "start_date"
    t.date "end_date"
    t.integer "minimum_amount_cents", default: 0, null: false
    t.boolean "eligible_for_demand_letter_fees", default: true, null: false
    t.boolean "eligible_for_eviction_fees", default: true, null: false
    t.boolean "eligible_for_property_cash_rebalance", default: true, null: false
    t.index ["company_id"], name: "index_management_contracts_on_company_id"
  end

  create_table "member_onboarding_assignments", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "configuration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "completed_at"
    t.datetime "notified_at"
    t.index ["completed_at"], name: "index_member_onboarding_assignments_on_completed_at"
    t.index ["configuration_id"], name: "index_member_onboarding_assignments_on_configuration_id"
    t.index ["notified_at", "completed_at", "created_at"], name: "index_onboarding_assignments_notified_completed_created"
    t.index ["tenant_id", "configuration_id"], name: "index_unique_onboarding_assignments_on_tenant_and_configuration", unique: true
    t.index ["tenant_id"], name: "index_member_onboarding_assignments_on_tenant_id", unique: true, where: "(completed_at IS NULL)"
  end

  create_table "member_onboarding_charge_memberships", force: :cascade do |t|
    t.bigint "charge_id", null: false
    t.bigint "charge_preset_id", null: false
    t.integer "kind", null: false
    t.boolean "recurring", null: false
    t.date "start_date"
    t.date "end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["charge_id"], name: "index_member_onboarding_charge_memberships_on_charge"
    t.index ["charge_preset_id"], name: "index_member_onboarding_charge_memberships_on_charge_preset_id"
  end

  create_table "member_onboarding_charges", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["configuration_id"], name: "index_member_onboarding_charges_on_configuration_id"
  end

  create_table "member_onboarding_completions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "configuration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["configuration_id"], name: "index_member_onboarding_completions_on_configuration_id"
    t.index ["tenant_id"], name: "index_member_onboarding_completions_on_tenant_id"
  end

  create_table "member_onboarding_configurations", force: :cascade do |t|
    t.string "name", null: false
    t.text "message"
    t.bigint "portfolio_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "archived_at"
    t.index ["name"], name: "index_member_onboarding_configurations_on_name", unique: true
    t.index ["portfolio_id"], name: "index_member_onboarding_configurations_on_portfolio_id"
  end

  create_table "member_onboarding_guarantors", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.boolean "collections_information", null: false
    t.string "additional_questions", default: [], null: false, array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "address", null: false
    t.boolean "drivers_license_number", null: false
    t.boolean "optional", default: false, null: false
    t.index ["configuration_id"], name: "index_member_onboarding_guarantors_on_configuration_id"
  end

  create_table "member_onboarding_information_collections", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.boolean "nickname", null: false
    t.boolean "address", null: false
    t.boolean "student_id", null: false
    t.boolean "school_year", null: false
    t.boolean "collections_information", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "drivers_license_number", null: false
    t.jsonb "additional_questions", default: {}, null: false
    t.index ["configuration_id"], name: "index_member_onboarding_info_collections_on_configuration_id"
  end

  create_table "member_onboarding_lease_agreements", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.bigint "lease_template_id"
    t.boolean "require_member_signature", null: false
    t.boolean "require_guarantor_signature", null: false
    t.date "lease_start_date", null: false
    t.date "lease_end_date", null: false
    t.bigint "proration", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "require_countersigner", default: true, null: false
    t.bigint "countersigner_id"
    t.index ["configuration_id"], name: "index_member_onboarding_lease_agreements_on_configuration_id"
    t.index ["countersigner_id"], name: "index_memb_onb_lease_agreements_on_countersigner_id"
    t.index ["lease_template_id"], name: "index_member_onboarding_lease_agreements_on_lease_template_id"
  end

  create_table "member_onboarding_membership_agreements", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.bigint "membership_template_id"
    t.boolean "require_member_signature", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "require_countersigner", default: false, null: false
    t.bigint "countersigner_id"
    t.boolean "require_guarantor_signature", default: false, null: false
    t.index ["configuration_id"], name: "index_member_onboard_membership_agrmnts_on_configuration_id"
    t.index ["countersigner_id"], name: "index_memb_onb_agreements_on_countersigner_id"
    t.index ["membership_template_id"], name: "index_member_onboard_membership_agrmnts_on_membership_template"
  end

  create_table "member_onboarding_property_memberships", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.bigint "property_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "enhanced", default: false, null: false
    t.boolean "auto_assign", default: false, null: false
    t.index ["configuration_id"], name: "index_member_onboarding_property_memberships_on_configuration"
    t.index ["property_id"], name: "index_member_onboarding_property_memberships_on_property_id"
    t.index ["property_id"], name: "index_unique_onboarding_property_memberships_on_property", unique: true, where: "(auto_assign = true)"
  end

  create_table "member_onboarding_risk_release_installments", force: :cascade do |t|
    t.bigint "risk_release_configuration_id", null: false
    t.date "date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["risk_release_configuration_id"], name: "index_member_onboarding_risk_release_on_installments"
  end

  create_table "member_onboarding_risk_releases", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.bigint "account_id", null: false
    t.integer "monthly_cost_cents", null: false
    t.date "coverage_start_date", null: false
    t.boolean "enrollment_required", null: false
    t.integer "billing_frequency", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "coverage_end_date", null: false
    t.index ["account_id"], name: "index_member_onboarding_risk_releases_on_account_id"
    t.index ["configuration_id"], name: "index_member_onboarding_risk_releases_on_configuration_id"
  end

  create_table "merchant_account_contacts", force: :cascade do |t|
    t.string "account_holder_type", null: false
    t.bigint "account_holder_id", null: false
    t.integer "payment_processor", null: false
    t.string "location_id", null: false
    t.string "reference_number", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["account_holder_type", "account_holder_id"], name: "index_merchant_account_contacts_on_account_holder"
  end

  create_table "merchant_accounts", force: :cascade do |t|
    t.bigint "bank_account_id"
    t.string "name", null: false
    t.integer "payment_processor", null: false
    t.string "location_id"
    t.string "user_id"
    t.string "user_api_key"
    t.boolean "ach_debit_enabled", default: false, null: false
    t.boolean "credit_card_enabled", default: false, null: false
    t.boolean "check_scanning_enabled", default: false, null: false
    t.integer "ach_debit_markup_cents", default: 0, null: false
    t.float "credit_card_rate", default: 0.0, null: false
    t.integer "check_scanning_markup_cents", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "product_transaction_id"
    t.string "developer_id"
    t.boolean "ach_credit_enabled", default: false, null: false
    t.integer "ach_credit_markup_cents", default: 0, null: false
    t.decimal "ach_debit_rate", default: "0.0", null: false
    t.decimal "ach_credit_rate", default: "0.0", null: false
    t.decimal "check_scanning_rate", default: "0.0", null: false
    t.integer "credit_card_markup_cents", default: 0, null: false
    t.integer "priority", default: 0, null: false
    t.index ["bank_account_id"], name: "index_merchant_accounts_on_bank_account_id"
  end

  create_table "mercury_configurations", force: :cascade do |t|
    t.string "username", null: false
    t.string "encrypted_password"
    t.string "encrypted_password_iv"
    t.string "encrypted_password_salt"
    t.string "mercury_username", null: false
    t.string "encrypted_mercury_password"
    t.string "encrypted_mercury_password_iv"
    t.string "encrypted_mercury_password_salt"
    t.string "inspection_template_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "mercury_orders", force: :cascade do |t|
    t.bigint "inspection_id"
    t.integer "tracking_id", null: false
    t.integer "status", default: 0, null: false
    t.text "data", null: false
    t.string "case_number", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["case_number"], name: "index_mercury_orders_on_case_number"
    t.index ["inspection_id"], name: "index_mercury_orders_on_inspection_id", unique: true
    t.index ["tracking_id"], name: "index_mercury_orders_on_tracking_id", unique: true
  end

  create_table "messages", id: :serial, force: :cascade do |t|
    t.text "body"
    t.integer "chat_room_id", null: false
    t.string "chat_room_type", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "author_type", null: false
    t.integer "author_id", null: false
  end

  create_table "messaging_contact_group_activities", force: :cascade do |t|
    t.bigint "contact_group_id", null: false
    t.integer "contact_method", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["contact_group_id"], name: "index_messaging_contact_group_activities_on_contact_group_id"
  end

  create_table "messaging_contact_group_memberships", force: :cascade do |t|
    t.bigint "contact_group_id", null: false
    t.string "contact_type", null: false
    t.bigint "contact_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["contact_group_id", "contact_id", "contact_type"], name: "index_contact_group_memberships_on_group_and_contactable", unique: true
    t.index ["contact_group_id"], name: "index_messaging_contact_group_memberships_on_contact_group_id"
    t.index ["contact_type", "contact_id"], name: "index_contact_group_memberships_on_contactable"
  end

  create_table "messaging_contact_groups", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.string "owned_by_type", null: false
    t.bigint "owned_by_id", null: false
    t.string "name", null: false
    t.text "description"
    t.integer "contacts_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["owned_by_type", "owned_by_id"], name: "index_contact_groups_on_owned_by"
  end

  create_table "messaging_emails", force: :cascade do |t|
    t.string "sender_type"
    t.bigint "sender_id"
    t.bigint "replying_to_id"
    t.string "subject"
    t.string "token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "thread_id", null: false
    t.boolean "unread", default: true, null: false
    t.string "regarding_type"
    t.bigint "regarding_id"
    t.index ["regarding_type", "regarding_id"], name: "index_messaging_emails_on_regarding"
    t.index ["replying_to_id"], name: "index_messaging_emails_on_replying_to_id"
    t.index ["sender_type", "sender_id"], name: "index_messaging_emails_on_sender_type_and_sender_id"
    t.index ["thread_id"], name: "index_messaging_emails_on_thread_id"
    t.index ["token"], name: "index_messaging_emails_on_token", unique: true
    t.index ["unread"], name: "index_messaging_emails_on_unread"
  end

  create_table "messaging_message_deliveries", force: :cascade do |t|
    t.bigint "message_id", null: false
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.integer "copied"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "unread", default: true, null: false
    t.index ["message_id"], name: "index_messaging_message_deliveries_on_message_id"
    t.index ["recipient_type", "recipient_id"], name: "index_messaging_message_deliveries_on_recipient"
  end

  create_table "messaging_simple_contacts", force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone", null: false
    t.string "email", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_messaging_simple_contacts_on_email"
    t.index ["first_name", "last_name", "phone", "email"], name: "index_contact_group_members_on_name_phone_email", unique: true
    t.index ["phone"], name: "index_messaging_simple_contacts_on_phone"
  end

  create_table "metadata", id: :serial, force: :cascade do |t|
    t.string "parent_type", null: false
    t.integer "parent_id", null: false
    t.jsonb "data", default: {}, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["parent_id", "parent_type"], name: "index_metadata_on_parent_id_and_parent_type", unique: true
    t.index ["parent_type", "parent_id"], name: "index_metadata_on_parent"
  end

  create_table "mobile_devices", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "firebase_token", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["firebase_token"], name: "index_mobile_devices_on_firebase_token"
    t.index ["user_id"], name: "index_mobile_devices_on_user_id"
  end

  create_table "morocco_profiles", force: :cascade do |t|
    t.string "phone", null: false
    t.integer "tunisia_customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["phone"], name: "index_morocco_profiles_on_phone", unique: true
  end

  create_table "notification_preferences", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "email_preference", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "notify_assigned_tasks", default: true, null: false
    t.boolean "notify_assigned_maintenance_tickets", default: true, null: false
    t.boolean "notify_submitted_lease_applications", default: false, null: false
    t.boolean "notify_submitted_guest_cards", default: false, null: false
    t.boolean "notify_available_screening_results", default: false, null: false
    t.boolean "notify_submitted_work_orders", default: false, null: false
    t.boolean "notify_owner_created_entity", default: false, null: false
    t.string "user_type", null: false
    t.boolean "notify_approval_required", default: true, null: false
    t.boolean "notify_maintenance_ticket_comments", default: true, null: false
    t.boolean "notify_portal_message_received", default: false, null: false
    t.boolean "notify_estimates", default: false, null: false
    t.boolean "notify_bids", default: false, null: false
    t.boolean "notify_submitted_reservations", default: false, null: false
    t.boolean "notify_owner_contribution", default: false, null: false
    t.boolean "notify_debit_card_transactions", default: true, null: false
    t.index ["user_id", "user_type"], name: "index_notification_preferences_on_user_id_and_user_type"
  end

  create_table "notifications", id: :serial, force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "title", null: false
    t.string "link", null: false
    t.string "description", null: false
    t.boolean "seen", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "resource_type"
    t.integer "resource_id"
    t.string "kind", null: false
    t.datetime "email_sent_at", precision: nil
    t.string "user_type", null: false
    t.bigint "attachment_id"
    t.index ["attachment_id"], name: "index_notifications_on_attachment_id"
    t.index ["resource_type", "resource_id"], name: "index_notifications_on_resource_type_and_resource_id"
    t.index ["user_id", "user_type"], name: "index_notifications_on_user_id_and_user_type"
  end

  create_table "oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.index ["application_id"], name: "index_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_grants_on_token", unique: true
  end

  create_table "oauth_access_tokens", force: :cascade do |t|
    t.bigint "resource_owner_id"
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.string "scopes"
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "previous_refresh_token", default: "", null: false
    t.index ["application_id"], name: "index_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_tokens_on_token", unique: true
  end

  create_table "oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_oauth_applications_on_uid", unique: true
  end

  create_table "occupancy_counts", id: :serial, force: :cascade do |t|
    t.string "space_type"
    t.integer "space_id"
    t.integer "tenant_count", default: 0, null: false
    t.integer "unit_count", default: 0, null: false
    t.integer "property_count", default: 0, null: false
    t.float "occupancy", default: 0.0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "vacant_unit_count", default: 0, null: false
    t.integer "company_count", default: 0, null: false
    t.index ["space_type", "space_id"], name: "index_occupancy_counts_on_space"
  end

  create_table "owner_contribution_allocations", force: :cascade do |t|
    t.bigint "owner_contribution_id", null: false
    t.bigint "property_id", null: false
    t.integer "amount_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["owner_contribution_id"], name: "index_owner_contribution_allocations_on_owner_contribution_id"
    t.index ["property_id"], name: "index_owner_contribution_allocations_on_property_id"
  end

  create_table "owner_contribution_requests", force: :cascade do |t|
    t.bigint "property_id", null: false
    t.bigint "owner_id", null: false
    t.bigint "requested_by_id", null: false
    t.integer "amount_cents", default: 0, null: false
    t.string "description", null: false
    t.string "message"
    t.datetime "expires_at", null: false
    t.datetime "completed_at"
    t.datetime "rejected_at"
    t.string "rejection_reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["owner_id"], name: "index_owner_contribution_requests_on_owner_id"
    t.index ["property_id"], name: "index_owner_contribution_requests_on_property_id"
    t.index ["requested_by_id"], name: "index_owner_contribution_requests_on_requested_by_id"
  end

  create_table "owner_contributions", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.integer "status", default: 0, null: false
    t.string "payment_method_type"
    t.bigint "payment_method_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "request_id"
    t.index ["company_id"], name: "index_owner_contributions_on_company_id"
    t.index ["payment_method_type", "payment_method_id"], name: "index_owner_contributions_on_payment_method"
    t.index ["request_id"], name: "index_owner_contributions_on_request_id"
  end

  create_table "owner_invites", force: :cascade do |t|
    t.string "email", null: false
    t.bigint "invited_by_id"
    t.string "token", null: false
    t.text "message"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["invited_by_id"], name: "index_owner_invites_on_invited_by_id"
  end

  create_table "owners", id: :serial, force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.date "date_of_birth"
    t.string "title"
    t.integer "kind", default: 0, null: false
    t.text "notes"
    t.datetime "archived_at", precision: nil
    t.index ["confirmation_token"], name: "index_owners_on_confirmation_token", unique: true
    t.index ["email"], name: "index_owners_on_email", unique: true
    t.index ["reset_password_token"], name: "index_owners_on_reset_password_token", unique: true
  end

  create_table "ownerships", force: :cascade do |t|
    t.bigint "entity_id", null: false
    t.string "owner_type", null: false
    t.bigint "owner_id", null: false
    t.decimal "amount", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["entity_id"], name: "index_ownerships_on_entity_id"
    t.index ["owner_type", "owner_id"], name: "index_ownerships_on_owner"
  end

  create_table "parking_allocations", force: :cascade do |t|
    t.bigint "lease_id", null: false
    t.integer "included_count", default: 0, null: false
    t.integer "purchased_count", default: 0, null: false
    t.integer "available_to_purchase_count", default: 0, null: false
    t.integer "space_price_cents", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["lease_id"], name: "index_parking_allocations_on_lease_id"
  end

  create_table "parking_lots", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.integer "space_count", default: 1, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "property_id"
    t.index ["property_id"], name: "index_parking_lots_on_property_id"
  end

  create_table "parking_reservations", id: :serial, force: :cascade do |t|
    t.integer "parking_lot_id", null: false
    t.integer "tenant_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "start_date"
    t.date "end_date"
    t.integer "monthly_amount_cents", default: 0, null: false
    t.string "name"
    t.string "tag_number"
    t.string "card_number"
    t.bigint "parking_space_id"
    t.bigint "lease_id"
    t.text "note"
    t.integer "kind", default: 0, null: false
    t.integer "space_count", default: 1, null: false
    t.index ["lease_id"], name: "index_parking_reservations_on_lease_id"
    t.index ["parking_space_id"], name: "index_parking_reservations_on_parking_space_id"
  end

  create_table "parking_spaces", force: :cascade do |t|
    t.bigint "parking_lot_id", null: false
    t.string "name", null: false
    t.integer "price_cents", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["parking_lot_id"], name: "index_parking_spaces_on_parking_lot_id"
  end

  create_table "partner_tokens", force: :cascade do |t|
    t.string "partner_name", null: false
    t.string "token_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["partner_name"], name: "index_partner_tokens_on_partner_name", unique: true
    t.index ["token_digest"], name: "index_partner_tokens_on_token_digest", unique: true
  end

  create_table "pay_lease_transactions", force: :cascade do |t|
    t.bigint "payment_id"
    t.string "pay_lease_id"
    t.string "trace_id", null: false
    t.integer "status", null: false
    t.integer "response_code", null: false
    t.string "response_message", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "merchant_account_id"
    t.index ["merchant_account_id"], name: "index_pay_lease_transactions_on_merchant_account_id"
    t.index ["payment_id"], name: "index_pay_lease_transactions_on_payment_id"
  end

  create_table "payment_batches", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "closed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "invoices_count", default: 0, null: false
    t.integer "invoices_total_amount_cents", default: 0, null: false
  end

  create_table "payment_plan_installments", force: :cascade do |t|
    t.bigint "payment_plan_id", null: false
    t.bigint "scheduled_payment_id", null: false
    t.date "date", null: false
    t.integer "amount_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["payment_plan_id"], name: "index_payment_plan_installments_on_payment_plan_id"
    t.index ["scheduled_payment_id"], name: "index_payment_plan_installments_on_scheduled_payment_id"
  end

  create_table "payment_plan_invoice_memberships", force: :cascade do |t|
    t.bigint "payment_plan_id", null: false
    t.bigint "invoice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["invoice_id"], name: "index_payment_plan_invoice_memberships_on_invoice_id"
    t.index ["payment_plan_id", "invoice_id"], name: "index_payment_invoice_memberships_on_unique_plan_and_invoice", unique: true
    t.index ["payment_plan_id"], name: "index_payment_plan_invoice_memberships_on_payment_plan_id"
  end

  create_table "payment_plan_preset_installments", force: :cascade do |t|
    t.bigint "preset_id", null: false
    t.date "date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["preset_id", "date"], name: "index_payment_plan_preset_installments_on_preset_id_and_date", unique: true
    t.index ["preset_id"], name: "index_payment_plan_preset_installments_on_preset_id"
  end

  create_table "payment_plan_presets", force: :cascade do |t|
    t.bigint "configuration_id", null: false
    t.string "type", null: false
    t.string "name", null: false
    t.date "date_available_start"
    t.date "date_available_end"
    t.boolean "portal_visible", default: true, null: false
    t.bigint "property_whitelist_ids", default: [], null: false, array: true
    t.bigint "property_blacklist_ids", default: [], null: false, array: true
    t.integer "minimum_installment_count"
    t.integer "maximum_installment_count"
    t.integer "alignment", default: 0, null: false
    t.integer "duration_days"
    t.date "range_start_date"
    t.date "range_end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["configuration_id"], name: "index_payment_plan_presets_on_configuration_id"
  end

  create_table "payment_plans", force: :cascade do |t|
    t.bigint "lease_membership_id"
    t.string "payment_method_type", null: false
    t.bigint "payment_method_id", null: false
    t.integer "amount_cents", default: 0, null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "simple_agreement_id"
    t.text "signature"
    t.inet "ip_address"
    t.bigint "preset_id"
    t.index ["lease_membership_id"], name: "index_payment_plans_on_lease_membership_id"
    t.index ["payment_method_type", "payment_method_id"], name: "index_payment_plans_on_payment_method"
    t.index ["preset_id"], name: "index_payment_plans_on_preset_id"
    t.index ["simple_agreement_id"], name: "index_payment_plans_on_simple_agreement_id"
  end

  create_table "payments", id: :serial, force: :cascade do |t|
    t.integer "amount_cents", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "date"
    t.string "description"
    t.integer "status", default: 0, null: false
    t.integer "kind", default: 0, null: false
    t.string "payer_type", null: false
    t.integer "payer_id", null: false
    t.string "check_number"
    t.bigint "debit_bank_account_id"
    t.bigint "credit_bank_account_id"
    t.bigint "batch_id"
    t.integer "convenience_fee_cents", default: 0, null: false
    t.text "note"
    t.date "reversed_at"
    t.string "payee_type", null: false
    t.bigint "payee_id", null: false
    t.bigint "payer_unit_id"
    t.bigint "payee_unit_id"
    t.bigint "payer_lease_membership_id"
    t.bigint "payee_lease_membership_id"
    t.bigint "scheduled_payment_id"
    t.bigint "payable_credit_note_account_id"
    t.bigint "receivable_credit_note_account_id"
    t.bigint "loan_id"
    t.index ["batch_id"], name: "index_payments_on_batch_id"
    t.index ["credit_bank_account_id"], name: "index_payments_on_credit_bank_account_id"
    t.index ["debit_bank_account_id"], name: "index_payments_on_debit_bank_account_id"
    t.index ["loan_id"], name: "index_payments_on_loan_id"
    t.index ["payable_credit_note_account_id"], name: "index_payments_on_payable_credit_note_account_id"
    t.index ["payee_lease_membership_id"], name: "index_payments_on_payee_lease_membership_id"
    t.index ["payee_type", "payee_id"], name: "index_payments_on_payee_type_and_payee_id"
    t.index ["payee_unit_id"], name: "index_payments_on_payee_unit_id"
    t.index ["payer_lease_membership_id"], name: "index_payments_on_payer_lease_membership_id"
    t.index ["payer_type", "payer_id"], name: "index_payments_on_payer_type_and_payer_id"
    t.index ["payer_unit_id"], name: "index_payments_on_payer_unit_id"
    t.index ["receivable_credit_note_account_id"], name: "index_payments_on_receivable_credit_note_account_id"
    t.index ["scheduled_payment_id"], name: "index_payments_on_scheduled_payment_id"
  end

  create_table "pets", force: :cascade do |t|
    t.integer "kind", null: false
    t.string "breed"
    t.string "color"
    t.integer "weight", null: false
    t.string "kind_detail"
    t.string "name", null: false
    t.boolean "service_animal", default: false, null: false
    t.bigint "lease_application_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "date_of_birth", null: false
    t.index ["lease_application_id"], name: "index_pets_on_lease_application_id"
  end

  create_table "phone_calls", force: :cascade do |t|
    t.integer "tenant_id"
    t.string "caller_phone_number", null: false
    t.string "callee_phone_number", null: false
    t.string "recording_url"
    t.text "transcription"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "plutus_accounts", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.string "type", null: false
    t.boolean "contra", default: false, null: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "tenant_id", null: false
    t.string "tenant_type", null: false
    t.string "category", null: false
    t.string "gl_code", null: false
    t.integer "parent_id"
    t.string "description"
    t.string "header"
    t.boolean "owner_suppressed", default: false, null: false
    t.boolean "passthrough", default: false, null: false
    t.index ["name", "type"], name: "index_plutus_accounts_on_name_and_type"
    t.index ["tenant_id", "tenant_type", "type", "category", "header", "name"], name: "index_unique_plutus_accounts_name", unique: true
    t.index ["tenant_id", "tenant_type"], name: "index_plutus_accounts_on_tenant_id_and_tenant_type"
  end

  create_table "plutus_amounts", id: :serial, force: :cascade do |t|
    t.string "type"
    t.integer "account_id"
    t.integer "entry_id"
    t.decimal "amount", precision: 20, scale: 10
    t.bigint "reconciliation_id"
    t.bigint "deposit_batch_id"
    t.string "note"
    t.index ["account_id", "entry_id"], name: "index_plutus_amounts_on_account_id_and_entry_id"
    t.index ["deposit_batch_id"], name: "index_plutus_amounts_on_deposit_batch_id"
    t.index ["entry_id", "account_id"], name: "index_plutus_amounts_on_entry_id_and_account_id"
    t.index ["reconciliation_id"], name: "index_plutus_amounts_on_reconciliation_id"
    t.index ["type"], name: "index_plutus_amounts_on_type"
  end

  create_table "plutus_entries", id: :serial, force: :cascade do |t|
    t.string "description"
    t.date "date"
    t.integer "commercial_document_id"
    t.string "commercial_document_type"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "property_id"
    t.integer "lease_membership_id"
    t.integer "journal_id", null: false
    t.integer "unit_id"
    t.integer "tenant_id"
    t.string "contact_name"
    t.boolean "retained_earnings", default: false, null: false
    t.integer "basis", default: 0, null: false
    t.string "reference_number"
    t.integer "kind", null: false
    t.string "reference_text"
    t.bigint "recurring_schedule_id"
    t.bigint "invoice_payment_id"
    t.index ["basis"], name: "index_plutus_entries_on_basis"
    t.index ["commercial_document_id", "commercial_document_type"], name: "index_entries_on_commercial_doc"
    t.index ["date"], name: "index_plutus_entries_on_date"
    t.index ["journal_id"], name: "index_plutus_entries_on_journal_id"
    t.index ["lease_membership_id"], name: "index_plutus_entries_on_lease_membership_id"
    t.index ["property_id"], name: "index_plutus_entries_on_property_id"
    t.index ["recurring_schedule_id"], name: "index_plutus_entries_on_recurring_schedule_id"
    t.index ["tenant_id"], name: "index_plutus_entries_on_tenant_id"
  end

  create_table "plutus_entry_automatic_reversals", force: :cascade do |t|
    t.date "date", null: false
    t.bigint "source_entry_id"
    t.bigint "destination_entry_id"
    t.boolean "processed", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["destination_entry_id"], name: "index_plutus_entry_automatic_reversals_on_destination_entry_id"
    t.index ["source_entry_id"], name: "index_plutus_entry_automatic_reversals_on_source_entry_id"
  end

  create_table "plutus_entry_linkages", force: :cascade do |t|
    t.bigint "left_entry_id", null: false
    t.bigint "right_entry_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["left_entry_id"], name: "index_plutus_entry_linkages_on_left_entry_id"
    t.index ["right_entry_id"], name: "index_plutus_entry_linkages_on_right_entry_id"
  end

  create_table "portfolios", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "configuration_id"
    t.boolean "setup", default: false, null: false
    t.string "description"
    t.index ["configuration_id"], name: "index_portfolios_on_configuration_id"
  end

  create_table "portfolios_bank_accounts", force: :cascade do |t|
    t.bigint "portfolio_id", null: false
    t.bigint "bank_account_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["bank_account_id"], name: "index_portfolios_bank_accounts_on_bank_account_id"
    t.index ["portfolio_id"], name: "index_portfolios_bank_accounts_on_portfolio_id"
  end

  create_table "profit_stars_transactions", id: :serial, force: :cascade do |t|
    t.integer "payment_id", null: false
    t.string "reference_number"
    t.string "status", default: "Pending", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "merchant_account_id"
    t.index ["merchant_account_id"], name: "index_profit_stars_transactions_on_merchant_account_id"
    t.index ["payment_id"], name: "index_profit_stars_transactions_on_payment_id"
  end

  create_table "project_bids", force: :cascade do |t|
    t.bigint "task_id", null: false
    t.bigint "vendor_id", null: false
    t.integer "amount_cents", null: false
    t.date "estimated_completion_date"
    t.datetime "selected_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["task_id"], name: "index_project_bids_on_task_id"
    t.index ["vendor_id"], name: "index_project_bids_on_vendor_id"
  end

  create_table "project_board_columns", force: :cascade do |t|
    t.bigint "board_id"
    t.integer "order", default: 0, null: false
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["board_id"], name: "index_project_board_columns_on_board_id"
  end

  create_table "project_boards", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "project_dependencies", force: :cascade do |t|
    t.bigint "task_id", null: false
    t.string "dependent_on_type", null: false
    t.bigint "dependent_on_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["dependent_on_type", "dependent_on_id"], name: "index_project_dependencies_on_dependant_on"
    t.index ["task_id"], name: "index_project_dependencies_on_task_id"
  end

  create_table "project_memberships", id: :serial, force: :cascade do |t|
    t.integer "project_id"
    t.integer "user_id"
    t.boolean "owner", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["project_id"], name: "index_project_memberships_on_project_id"
  end

  create_table "project_phases", force: :cascade do |t|
    t.bigint "project_id", null: false
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "order", null: false
    t.index ["project_id"], name: "index_project_phases_on_project_id"
  end

  create_table "projects", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.date "completed_at"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "template", default: false, null: false
    t.boolean "personal_template", default: false, null: false
    t.bigint "estimate_id"
    t.bigint "board_id", null: false
    t.bigint "column_id", null: false
    t.integer "order", default: 0, null: false
    t.boolean "public", default: true, null: false
    t.bigint "property_id"
    t.index ["board_id"], name: "index_projects_on_board_id"
    t.index ["column_id"], name: "index_projects_on_column_id"
    t.index ["estimate_id"], name: "index_projects_on_estimate_id"
    t.index ["property_id"], name: "index_projects_on_property_id"
  end

  create_table "projects_tickets", id: :serial, force: :cascade do |t|
    t.integer "project_id"
    t.integer "maintenance_ticket_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["maintenance_ticket_id"], name: "index_projects_tickets_on_maintenance_ticket_id"
    t.index ["project_id"], name: "index_projects_tickets_on_project_id"
  end

  create_table "properties", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "company_id", null: false
    t.integer "year_built"
    t.integer "kind", null: false
    t.boolean "setup", default: false, null: false
    t.date "date_acquired"
    t.bigint "configuration_id"
    t.bigint "electric_utility_id"
    t.bigint "gas_utility_id"
    t.bigint "recycling_utility_id"
    t.bigint "sewer_utility_id"
    t.bigint "trash_utility_id"
    t.bigint "water_utility_id"
    t.string "phone"
    t.string "note"
    t.datetime "archived_at", precision: nil
    t.text "notes"
    t.date "rental_license_expiration_date"
    t.datetime "self_archived_at", precision: nil
    t.bigint "archived_at_from_id"
    t.string "archived_at_from_type"
    t.integer "electric_utility_responsibility"
    t.integer "gas_utility_responsibility"
    t.integer "recycling_utility_responsibility"
    t.integer "sewer_utility_responsibility"
    t.integer "trash_utility_responsibility"
    t.integer "water_utility_responsibility"
    t.boolean "preleasing", default: false, null: false
    t.string "subdivision"
    t.index ["configuration_id"], name: "index_properties_on_configuration_id"
    t.index ["electric_utility_id"], name: "index_properties_on_electric_utility_id"
    t.index ["gas_utility_id"], name: "index_properties_on_gas_utility_id"
    t.index ["recycling_utility_id"], name: "index_properties_on_recycling_utility_id"
    t.index ["sewer_utility_id"], name: "index_properties_on_sewer_utility_id"
    t.index ["trash_utility_id"], name: "index_properties_on_trash_utility_id"
    t.index ["water_utility_id"], name: "index_properties_on_water_utility_id"
  end

  create_table "property_analyses", force: :cascade do |t|
    t.bigint "property_id", null: false
    t.integer "deed_type", null: false
    t.string "name_on_deed", null: false
    t.date "purchase_date", null: false
    t.bigint "purchase_price_cents", default: 0, null: false
    t.bigint "estimated_value_cents", default: 0, null: false
    t.bigint "net_operating_income_cents", default: 0, null: false
    t.decimal "interest_rate", null: false
    t.bigint "interest_expense_cents", default: 0, null: false
    t.bigint "mortgage_balance_cents", default: 0, null: false
    t.bigint "total_cash_invested_cents", default: 0, null: false
    t.bigint "monthly_mortgage_payment_cents", default: 0, null: false
    t.bigint "insurance_premium_cents", default: 0, null: false
    t.bigint "property_taxes_cents", default: 0, null: false
    t.bigint "hoa_dues_cents", default: 0, null: false
    t.bigint "utilities_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["property_id"], name: "index_property_analyses_on_property_id"
  end

  create_table "property_managers", id: :serial, force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.string "username"
    t.boolean "top_level", default: false, null: false
    t.bigint "role_id", null: false
    t.datetime "archived_at", precision: nil
    t.string "phone"
    t.string "title"
    t.integer "billing_rate_cents", default: 0, null: false
    t.string "favorite_reports", default: [], null: false, array: true
    t.datetime "last_whats_new_visit", precision: nil
    t.index ["confirmation_token"], name: "index_property_managers_on_confirmation_token", unique: true
    t.index ["email"], name: "index_property_managers_on_email", unique: true
    t.index ["reset_password_token"], name: "index_property_managers_on_reset_password_token", unique: true
    t.index ["role_id"], name: "index_property_managers_on_role_id"
  end

  create_table "property_memberships", id: :serial, force: :cascade do |t|
    t.string "target_type", null: false
    t.integer "target_id", null: false
    t.integer "user_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["target_type", "target_id"], name: "index_property_memberships_on_target"
    t.index ["user_id"], name: "index_property_memberships_on_user_id"
  end

  create_table "property_parcels", force: :cascade do |t|
    t.bigint "property_id", null: false
    t.string "parcel_id", null: false
    t.string "legal_description"
    t.decimal "acreage"
    t.integer "land_value_cents"
    t.integer "building_value_cents"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["property_id"], name: "index_property_parcels_on_property_id"
  end

  create_table "property_transfers", force: :cascade do |t|
    t.bigint "source_property_id", null: false
    t.bigint "destination_property_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["destination_property_id"], name: "index_property_transfers_on_destination_property_id"
    t.index ["source_property_id"], name: "index_property_transfers_on_source_property_id"
  end

  create_table "rbac_permissions", force: :cascade do |t|
    t.bigint "role_id", null: false
    t.string "namespace", null: false
    t.string "component", null: false
    t.string "action", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["namespace", "component", "action"], name: "index_rbac_permissions_on_namespace_and_component_and_action"
    t.index ["namespace", "component"], name: "index_rbac_permissions_on_namespace_and_component"
    t.index ["namespace"], name: "index_rbac_permissions_on_namespace"
    t.index ["role_id", "namespace", "component", "action"], name: "index_permissions_on_role_and_nca", unique: true
    t.index ["role_id"], name: "index_rbac_permissions_on_role_id"
  end

  create_table "reams_configurations", force: :cascade do |t|
    t.string "username", null: false
    t.string "encrypted_password"
    t.string "encrypted_password_iv"
    t.string "encrypted_password_salt"
    t.string "client_app_name", null: false
    t.string "vendor_name", null: false
    t.string "inspection_template_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "template", default: {}, null: false
  end

  create_table "reams_orders", force: :cascade do |t|
    t.bigint "inspection_id"
    t.string "reams_id", null: false
    t.integer "status", default: 0, null: false
    t.json "data", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "submitted_photo_ids", default: [], null: false, array: true
    t.string "case_number", null: false
    t.index ["case_number"], name: "index_reams_orders_on_case_number"
    t.index ["inspection_id"], name: "index_reams_orders_on_inspection_id", unique: true
    t.index ["reams_id"], name: "index_reams_orders_on_reams_id", unique: true
  end

  create_table "recurring_schedules", force: :cascade do |t|
    t.date "start_date", null: false
    t.integer "repeat_frequency", default: 1, null: false
    t.integer "repeat_interval", default: 2, null: false
    t.integer "maximum_occurrences"
    t.string "schedule", null: false
    t.integer "remaining_occurrences"
    t.datetime "last_ran_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "reports", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "description"
    t.text "sql_query"
    t.string "type", default: "Report", null: false
    t.integer "category", default: 0, null: false
    t.boolean "owner_visible", default: false, null: false
  end

  create_table "reports_email_schedules", force: :cascade do |t|
    t.bigint "created_by_id", null: false
    t.string "recipients", default: [], null: false, array: true
    t.string "subject", null: false
    t.string "body"
    t.jsonb "filters", default: {}, null: false
    t.string "schedule", null: false
    t.integer "date_range"
    t.boolean "pdf_format", default: false, null: false
    t.boolean "xlsx_format", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "last_ran_at", precision: nil
    t.string "slug", null: false
    t.boolean "skip_empty", default: false, null: false
    t.index ["created_by_id"], name: "index_reports_email_schedules_on_created_by_id"
  end

  create_table "reports_packet_template_entries", force: :cascade do |t|
    t.bigint "packet_template_id", null: false
    t.string "slug", null: false
    t.integer "order", default: 0, null: false
    t.json "filters", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["packet_template_id"], name: "index_reports_packet_template_entries_on_packet_template_id"
  end

  create_table "reports_packet_templates", force: :cascade do |t|
    t.string "name", null: false
    t.integer "format", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "risk_release_enrollments", force: :cascade do |t|
    t.bigint "simple_agreement_membership_id"
    t.bigint "lease_membership_id"
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lease_membership_id"], name: "index_risk_release_enrollments_on_lease_membership_id"
    t.index ["simple_agreement_membership_id"], name: "index_risk_release_enrollments_on_simple_agreement_membership"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "access_all_reports", default: true, null: false
    t.string "report_whitelist", default: [], null: false, array: true
    t.boolean "administrator", default: false, null: false
    t.boolean "can_print_checks", default: false, null: false
    t.index ["name"], name: "index_roles_on_name", unique: true
  end

  create_table "rooms", force: :cascade do |t|
    t.bigint "property_id", null: false
    t.bigint "unit_id"
    t.integer "kind", null: false
    t.string "nickname"
    t.integer "floor"
    t.integer "square_feet"
    t.datetime "archived_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["property_id"], name: "index_rooms_on_property_id"
    t.index ["unit_id"], name: "index_rooms_on_unit_id"
  end

  create_table "saferent_credentials", force: :cascade do |t|
    t.string "target_type", null: false
    t.bigint "target_id", null: false
    t.string "encrypted_user_account"
    t.string "encrypted_user_account_iv"
    t.string "encrypted_user_account_salt"
    t.string "encrypted_user_name"
    t.string "encrypted_user_name_iv"
    t.string "encrypted_user_name_salt"
    t.string "encrypted_user_password"
    t.string "encrypted_user_password_iv"
    t.string "encrypted_user_password_salt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["target_type", "target_id"], name: "index_saferent_credentials_on_target"
  end

  create_table "saferent_screenings", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.string "report_name", null: false
    t.integer "status", default: 0, null: false
    t.string "raw_status"
    t.string "transaction_number"
    t.string "report_date"
    t.string "application_decision"
    t.string "application_decision_description"
    t.integer "score"
    t.string "external_report_url"
    t.text "encrypted_report_response"
    t.string "encrypted_report_response_iv"
    t.string "encrypted_report_response_salt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lease_application_id"], name: "index_saferent_screenings_on_lease_application_id"
  end

  create_table "salesforce_accounts", force: :cascade do |t|
    t.string "instance_url", null: false
    t.string "client_id", null: false
    t.string "client_secret", null: false
    t.string "username", null: false
    t.string "password", null: false
    t.string "security_token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "scheduled_payments", id: :serial, force: :cascade do |t|
    t.date "date", null: false
    t.boolean "recurring", default: false, null: false
    t.boolean "pay_balance", default: false, null: false
    t.datetime "last_ran_at", precision: nil
    t.integer "amount_cents"
    t.string "source_type"
    t.integer "source_id"
    t.string "destination_type"
    t.integer "destination_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "description"
    t.datetime "reminder_sent_at", precision: nil
    t.string "payment_method_type"
    t.bigint "payment_method_id"
    t.integer "status", default: 0, null: false
    t.string "failure_reason"
    t.bigint "lease_membership_id"
    t.bigint "property_id", null: false
    t.bigint "simple_agreement_id"
    t.index ["destination_type", "destination_id"], name: "index_scheduled_payments_on_destination"
    t.index ["lease_membership_id"], name: "index_scheduled_payments_on_lease_membership_id"
    t.index ["payment_method_type", "payment_method_id"], name: "index_scheduled_payments_on_payment_methods"
    t.index ["property_id"], name: "index_scheduled_payments_on_property_id"
    t.index ["simple_agreement_id"], name: "index_scheduled_payments_on_simple_agreement_id"
    t.index ["source_type", "source_id"], name: "index_scheduled_payments_on_source"
  end

  create_table "sierra_leone_inspection_orders", force: :cascade do |t|
    t.bigint "inspection_report_id", null: false
    t.string "sierra_leone_inspection_order_id"
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["inspection_report_id"], name: "index_sierra_leone_inspection_orders_on_inspection_report_id", unique: true
    t.index ["sierra_leone_inspection_order_id"], name: "idx_sierra_leone_inspection_order_id", unique: true
  end

  create_table "sierra_leone_vendor_tokens", force: :cascade do |t|
    t.bigint "vendor_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "customer_id", null: false
    t.index ["vendor_id"], name: "index_sierra_leone_vendor_tokens_on_vendor_id"
  end

  create_table "sku_list_items", force: :cascade do |t|
    t.bigint "vendor_id", null: false
    t.string "category"
    t.string "description", null: false
    t.string "sku", null: false
    t.integer "price_cents", default: 0, null: false
    t.integer "labor_cents", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "brand"
    t.index ["vendor_id"], name: "index_sku_list_items_on_vendor_id"
  end

  create_table "sources", id: :serial, force: :cascade do |t|
    t.string "source"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "syndication_listing_supplements", force: :cascade do |t|
    t.bigint "listing_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_syndication_listing_supplements_on_listing_id"
  end

  create_table "syndication_property_supplements", force: :cascade do |t|
    t.bigint "zillow_claim_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["zillow_claim_id"], name: "index_syndication_property_supplements_on_zillow_claim_id"
  end

  create_table "syndication_supplements", force: :cascade do |t|
    t.bigint "listing_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_syndication_supplements_on_listing_id"
  end

  create_table "taggings", id: :serial, force: :cascade do |t|
    t.integer "tag_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "taggable_id", null: false
    t.string "taggable_type", null: false
    t.index ["taggable_id"], name: "index_taggings_on_taggable_id"
  end

  create_table "tags", id: :serial, force: :cascade do |t|
    t.string "tag", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "taggings_count", default: 0, null: false
    t.string "taggable_type", null: false
    t.index ["taggable_type", "tag"], name: "index_tags_on_taggable_type_and_tag", unique: true
  end

  create_table "tasks", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.json "attachments"
    t.integer "project_id"
    t.datetime "completed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "phase_id"
    t.datetime "due_date", precision: nil
    t.boolean "approval_required", default: false
    t.datetime "approval_requested_at", precision: nil
    t.bigint "approval_requested_by_id"
    t.datetime "approved_at", precision: nil
    t.bigint "approved_by_id"
    t.bigint "approving_role_id"
    t.integer "kind", default: 0, null: false
    t.integer "required_bid_count"
    t.index ["approval_requested_by_id"], name: "index_tasks_on_approval_requested_by_id"
    t.index ["approved_by_id"], name: "index_tasks_on_approved_by_id"
    t.index ["approving_role_id"], name: "index_tasks_on_approving_role_id"
    t.index ["project_id"], name: "index_tasks_on_project_id"
  end

  create_table "taxes_batch_groups", force: :cascade do |t|
    t.integer "status", default: 1, null: false
    t.integer "filing_period", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "taxes_irs_filings", force: :cascade do |t|
    t.string "payee_type", null: false
    t.bigint "payee_id", null: false
    t.bigint "payer_id", null: false
    t.integer "adjustment_cents", default: 0, null: false
    t.integer "filing_period", null: false
    t.string "form_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["payee_type", "payee_id"], name: "index_taxes_irs_filings_on_payee"
    t.index ["payer_id", "payee_id", "payee_type", "filing_period"], name: "irs_filing_uniqueness", unique: true
    t.index ["payer_id"], name: "index_taxes_irs_filings_on_payer_id"
  end

  create_table "taxes_nelco_accounts", force: :cascade do |t|
    t.string "username", null: false
    t.string "email", null: false
    t.string "encrypted_password", null: false
    t.string "encrypted_password_iv", null: false
    t.bigint "submitted_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["submitted_by_id"], name: "index_taxes_nelco_accounts_on_submitted_by_id"
  end

  create_table "taxes_nelco_batches", force: :cascade do |t|
    t.bigint "submitted_by_id"
    t.bigint "created_by_id"
    t.bigint "taxes_batch_group_id"
    t.datetime "submitted_at"
    t.string "nelco_batch_oid"
    t.integer "error_code"
    t.text "error"
    t.boolean "eula_consent"
    t.integer "filing_period", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "file_case", default: "initial", null: false
    t.index ["created_by_id"], name: "index_taxes_nelco_batches_on_created_by_id"
    t.index ["submitted_by_id"], name: "index_taxes_nelco_batches_on_submitted_by_id"
    t.index ["taxes_batch_group_id"], name: "index_taxes_nelco_batches_on_taxes_batch_group_id"
  end

  create_table "taxes_nelco_submissions", force: :cascade do |t|
    t.bigint "taxes_nelco_batch_id"
    t.bigint "taxes_irs_filing_id", null: false
    t.integer "payments_total_cents", null: false
    t.integer "adjustment_cents", default: 0, null: false
    t.integer "filing_period", null: false
    t.boolean "resubmission", default: false, null: false
    t.integer "status", default: 0, null: false
    t.text "error"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["taxes_irs_filing_id"], name: "index_taxes_nelco_submissions_on_taxes_irs_filing_id"
    t.index ["taxes_nelco_batch_id"], name: "index_taxes_nelco_submissions_on_taxes_nelco_batch_id"
  end

  create_table "taxpayer_identifications", id: :serial, force: :cascade do |t|
    t.string "taxpayer_type", null: false
    t.integer "taxpayer_id", null: false
    t.integer "tin_type", default: 0, null: false
    t.string "encrypted_tin", null: false
    t.string "encrypted_tin_iv", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["taxpayer_type", "taxpayer_id"], name: "index_taxpayer_identifications_on_taxpayer"
  end

  create_table "telephony_phone_numbers", force: :cascade do |t|
    t.string "twilio_sid", null: false
    t.string "number", null: false
    t.string "friendly_name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "locality"
    t.string "twilio_service_sid", null: false
  end

  create_table "telephony_text_messages", force: :cascade do |t|
    t.string "twilio_sid", null: false
    t.string "to", null: false
    t.string "from", null: false
    t.string "body", null: false
    t.string "status", null: false
    t.string "sender_type"
    t.bigint "sender_id"
    t.string "recipient_type"
    t.bigint "recipient_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["recipient_type", "recipient_id"], name: "index_telephony_text_messages_on_recipient"
    t.index ["sender_type", "sender_id"], name: "index_telephony_text_messages_on_sender"
  end

  create_table "telephony_twilio_proxy_sessions", force: :cascade do |t|
    t.string "resource_type", null: false
    t.bigint "resource_id", null: false
    t.string "sid", null: false
    t.string "participant_one_sid", null: false
    t.string "participant_one_type", null: false
    t.bigint "participant_one_id", null: false
    t.string "participant_two_sid", null: false
    t.string "participant_two_type", null: false
    t.bigint "participant_two_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["participant_one_type", "participant_one_id"], name: "index_twilio_proxy_sessions_on_participant_one"
    t.index ["participant_two_type", "participant_two_id"], name: "index_twilio_proxy_sessions_on_participant_two"
    t.index ["resource_type", "resource_id"], name: "index_twilio_proxy_sessions_on_resource"
  end

  create_table "templates", force: :cascade do |t|
    t.string "name", null: false
    t.integer "kind", null: false
    t.json "prefills"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tenants", id: :serial, force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "email"
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.string "phone"
    t.integer "kind", default: 0, null: false
    t.integer "lead_property_id"
    t.string "lead_source"
    t.date "date_of_birth"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.integer "temperature"
    t.bigint "leasing_agent_id"
    t.datetime "agreed_to_sms_at", precision: nil
    t.string "notes"
    t.boolean "electronic_payments_disabled", default: false, null: false
    t.string "external_id"
    t.index ["confirmation_token"], name: "index_tenants_on_confirmation_token", unique: true
    t.index ["email"], name: "index_tenants_on_email", unique: true
    t.index ["external_id"], name: "index_tenants_on_external_id", unique: true
    t.index ["lead_property_id"], name: "index_tenants_on_lead_property_id"
    t.index ["leasing_agent_id"], name: "index_tenants_on_leasing_agent_id"
    t.index ["reset_password_token"], name: "index_tenants_on_reset_password_token", unique: true
  end

  create_table "the_closing_docs_screening_groups", force: :cascade do |t|
    t.bigint "lease_application_id", null: false
    t.integer "status", default: 0, null: false
    t.string "api_id"
    t.jsonb "data"
    t.jsonb "report_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lease_application_id"], name: "index_the_closing_docs_screening_groups_on_lease_application_id"
  end

  create_table "todo_lists", id: :serial, force: :cascade do |t|
    t.boolean "template", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "parent_type"
    t.integer "parent_id"
    t.index ["parent_type", "parent_id"], name: "index_todo_lists_on_parent_type_and_parent_id"
  end

  create_table "todos", id: :serial, force: :cascade do |t|
    t.integer "todo_list_id", null: false
    t.string "body", null: false
    t.datetime "completed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "completed_by_id"
    t.string "completed_by_type"
    t.string "assigned_to_type"
    t.bigint "assigned_to_id"
    t.index ["assigned_to_type", "assigned_to_id"], name: "index_todos_on_assigned_to_type_and_assigned_to_id"
  end

  create_table "tours", force: :cascade do |t|
    t.bigint "guest_card_id"
    t.bigint "tour_guide_id"
    t.datetime "time", precision: nil, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "lead_id"
    t.bigint "property_id"
    t.string "message"
    t.datetime "followup_sent_at", precision: nil
    t.boolean "self_guided", default: false, null: false
    t.string "smart_rent_id"
    t.bigint "source_id"
    t.index ["guest_card_id"], name: "index_tours_on_guest_card_id"
    t.index ["lead_id"], name: "index_tours_on_lead_id"
    t.index ["property_id"], name: "index_tours_on_property_id"
    t.index ["source_id"], name: "index_tours_on_source_id"
    t.index ["tour_guide_id"], name: "index_tours_on_tour_guide_id"
  end

  create_table "tunisia_application_forms", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "created_by_id", null: false
    t.string "unit_id", null: false
    t.string "url", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_tunisia_application_forms_on_company_id"
    t.index ["created_by_id"], name: "index_tunisia_application_forms_on_created_by_id"
  end

  create_table "tunisia_authorized_users", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.string "email", null: false
    t.string "phone", null: false
    t.boolean "primary", null: false
    t.string "jwt_subject"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.virtual "normalized_email", type: :string, as: "\nCASE\n    WHEN (email IS NULL) THEN NULL::text\n    ELSE lower(ltrim(rtrim((email)::text)))\nEND", stored: true
    t.string "last_name"
    t.string "first_name"
    t.index ["company_id"], name: "index_tunisia_authorized_users_on_company_id"
    t.index ["email", "company_id"], name: "index_taus_on_emails_and_company_id", unique: true
  end

  create_table "tunisia_check_payment_additional_verifications", force: :cascade do |t|
    t.bigint "deposit_account_id", null: false
    t.bigint "tunisia_check_payment_id", null: false
    t.datetime "expires_at", null: false
    t.integer "amount_cents", null: false
    t.datetime "approved_at"
    t.datetime "rejected_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deposit_account_id"], name: "index_tunisia_check_verifications_on_deposit_accounts"
    t.index ["tunisia_check_payment_id"], name: "index_tunisia_check_verifications_on_tunisia_check_payment", unique: true
  end

  create_table "tunisia_deposit_accounts", force: :cascade do |t|
    t.bigint "bank_account_id", null: false
    t.bigint "opened_by_id", null: false
    t.string "tunisia_id", null: false
    t.string "deposit_product", null: false
    t.integer "status", null: false
    t.string "freeze_reason"
    t.string "close_reason"
    t.string "fraud_reason"
    t.integer "balance_cents", default: 0, null: false
    t.integer "hold_cents", default: 0, null: false
    t.integer "available_cents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "agreement_acknowledged_at", null: false
    t.index ["bank_account_id"], name: "index_tunisia_deposit_accounts_on_bank_account_id"
    t.index ["opened_by_id"], name: "index_tunisia_deposit_accounts_on_opened_by_id"
  end

  create_table "unit_downtimes", force: :cascade do |t|
    t.bigint "unit_id", null: false
    t.integer "reason", null: false
    t.date "start_date", null: false
    t.date "end_date"
    t.string "note"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["unit_id"], name: "index_unit_downtimes_on_unit_id"
  end

  create_table "unit_reservations", force: :cascade do |t|
    t.bigint "unit_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_unit_reservations_on_tenant_id"
    t.index ["unit_id"], name: "index_unit_reservations_on_unit_id"
  end

  create_table "units", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "property_id", null: false
    t.integer "floorplan_id", null: false
    t.integer "lihtc_designation"
    t.bigint "building_id"
    t.datetime "archived_at", precision: nil
    t.datetime "self_archived_at", precision: nil
    t.string "archived_at_from_type"
    t.bigint "archived_at_from_id"
    t.integer "floor"
    t.integer "affordable_designation", default: 0, null: false
    t.string "api_v2_status"
    t.index ["archived_at_from_type", "archived_at_from_id"], name: "index_units_on_archived_at_from_type_and_archived_at_from_id"
    t.index ["building_id"], name: "index_units_on_building_id"
    t.index ["floorplan_id"], name: "index_units_on_floorplan_id"
    t.index ["property_id"], name: "index_units_on_property_id"
  end

  create_table "user_accounts", force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "email", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["confirmation_token"], name: "index_user_accounts_on_confirmation_token", unique: true
    t.index ["email"], name: "index_user_accounts_on_email", unique: true
    t.index ["reset_password_token"], name: "index_user_accounts_on_reset_password_token", unique: true
  end

  create_table "user_invites", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.string "profile_type", null: false
    t.bigint "profile_id", null: false
    t.string "created_by_type"
    t.bigint "created_by_id"
    t.string "token", null: false
    t.datetime "accepted_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_type", "created_by_id"], name: "index_user_invites_on_created_by"
    t.index ["customer_id"], name: "index_user_invites_on_customer_id"
    t.index ["profile_type", "profile_id"], name: "index_user_invites_on_profile"
  end

  create_table "user_login_fingerprints", force: :cascade do |t|
    t.string "account_type", null: false
    t.bigint "account_id", null: false
    t.bigint "customer_id"
    t.inet "ip_address", null: false
    t.string "user_agent", null: false
    t.string "location"
    t.string "token", null: false
    t.datetime "confirmation_sent_at"
    t.datetime "confirmed_at"
    t.datetime "last_used_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_type", "account_id"], name: "index_user_login_fingerprints_on_account"
    t.index ["customer_id"], name: "index_user_login_fingerprints_on_customer_id"
    t.index ["ip_address", "account_id", "account_type", "customer_id"], name: "index_login_fingerprints_on_ip_address_and_account", unique: true
  end

  create_table "user_profiles", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "customer_id"
    t.string "profile_type", null: false
    t.bigint "profile_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_user_profiles_on_account_id"
    t.index ["customer_id"], name: "index_user_profiles_on_customer_id"
  end

  create_table "utilities_transfers", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "utility_id", null: false
    t.bigint "service_location_id", null: false
    t.datetime "submitted_at", precision: nil
    t.date "transfer_date"
    t.string "account_number"
    t.string "confirmation_number"
    t.jsonb "document_data"
    t.text "signature"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "kind", default: 0
    t.index ["service_location_id"], name: "index_utilities_transfers_on_service_location_id"
    t.index ["tenant_id"], name: "index_utilities_transfers_on_tenant_id"
    t.index ["utility_id"], name: "index_utilities_transfers_on_utility_id"
  end

  create_table "vehicles", id: :serial, force: :cascade do |t|
    t.string "make"
    t.string "model"
    t.integer "year"
    t.string "color"
    t.string "license_plate"
    t.integer "parking_reservation_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "state"
    t.bigint "lease_application_id"
    t.index ["lease_application_id"], name: "index_vehicles_on_lease_application_id"
  end

  create_table "vendor_assignments", id: :serial, force: :cascade do |t|
    t.integer "vendor_id", null: false
    t.integer "maintenance_ticket_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "token"
    t.datetime "invoice_request_sent_at", precision: nil
    t.integer "created_by_id"
    t.text "message"
    t.boolean "include_tenant_information", default: true, null: false
    t.datetime "accepted_at", precision: nil
    t.datetime "declined_at", precision: nil
    t.bigint "project_id"
    t.boolean "proof_required", default: false, null: false
    t.boolean "include_owner_information", default: false, null: false
    t.integer "declined_reason"
    t.index ["maintenance_ticket_id", "vendor_id"], name: "index_vendor_assignments_on_maintenance_ticket_id_and_vendor_id", unique: true
    t.index ["project_id"], name: "index_vendor_assignments_on_project_id"
  end

  create_table "vendor_contacts", id: :serial, force: :cascade do |t|
    t.integer "vendor_id", null: false
    t.string "first_name", null: false
    t.string "last_name"
    t.string "email"
    t.string "phone"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "vendor_contract_property_memberships", force: :cascade do |t|
    t.bigint "contract_id", null: false
    t.bigint "property_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["contract_id"], name: "index_vendor_contract_property_memberships_on_contract_id"
    t.index ["property_id"], name: "index_vendor_contract_property_memberships_on_property_id"
  end

  create_table "vendor_contracts", force: :cascade do |t|
    t.bigint "vendor_id", null: false
    t.bigint "entity_id", null: false
    t.string "name", null: false
    t.date "start_date"
    t.date "end_date"
    t.string "notes"
    t.datetime "archived_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_id"], name: "index_vendor_contracts_on_entity_id"
    t.index ["vendor_id"], name: "index_vendor_contracts_on_vendor_id"
  end

  create_table "vendor_invites", force: :cascade do |t|
    t.bigint "vendor_id", null: false
    t.string "token", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["token"], name: "index_vendor_invites_on_token", unique: true
    t.index ["vendor_id"], name: "index_vendor_invites_on_vendor_id"
  end

  create_table "vendors", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "kind", default: 0, null: false
    t.datetime "archived_at", precision: nil
    t.string "website"
    t.integer "business_type"
    t.text "notes"
    t.boolean "unconsolidated_checks", default: false, null: false
    t.integer "preferred_disbursement"
  end

  create_table "waitlist_entries", force: :cascade do |t|
    t.bigint "floorplan_id", null: false
    t.bigint "lease_application_id", null: false
    t.datetime "archived_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["floorplan_id"], name: "index_waitlist_entries_on_floorplan_id"
    t.index ["lease_application_id"], name: "index_waitlist_entries_on_lease_application_id"
  end

  create_table "whats_new_entries", force: :cascade do |t|
    t.date "date", null: false
    t.string "title", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "published_at", precision: nil
  end

  create_table "zeamster_transactions", force: :cascade do |t|
    t.bigint "payment_id", null: false
    t.string "zeamster_id", null: false
    t.integer "status", default: 131, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "location_id"
    t.integer "reason_code"
    t.datetime "last_refreshed_at", precision: nil
    t.index ["payment_id"], name: "index_zeamster_transactions_on_payment_id"
  end

  create_table "zillow_claims", force: :cascade do |t|
    t.string "hotpads_unit_number"
    t.string "zrm_property_id"
    t.integer "hotpads_property_type", null: false
    t.boolean "show_address", default: true, null: false
    t.bigint "agent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "grouping", default: 0, null: false
    t.index ["agent_id"], name: "index_zillow_claims_on_agent_id"
  end

  create_table "zillow_claims_properties", force: :cascade do |t|
    t.bigint "zillow_claim_id", null: false
    t.bigint "property_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["property_id"], name: "index_zillow_claims_properties_on_property_id"
    t.index ["zillow_claim_id", "property_id"], name: "index_zillow_claim_properties_unique_join", unique: true
    t.index ["zillow_claim_id"], name: "index_zillow_claims_properties_on_zillow_claim_id"
  end

  add_foreign_key "accounting_debit_card_purchases", "bank_accounts"
  add_foreign_key "accounting_debit_card_purchases", "invoices"
  add_foreign_key "accounting_debit_card_purchases", "plutus_accounts", column: "account_id"
  add_foreign_key "accounting_debit_card_purchases", "properties"
  add_foreign_key "accounting_debit_card_purchases", "vendors"
  add_foreign_key "accounting_missing_receipt_affidavits", "accounting_debit_card_purchases", column: "debit_card_purchase_id"
  add_foreign_key "accounting_statements", "companies"
  add_foreign_key "accounting_statements", "property_managers", column: "approved_by_id"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "admin_user_customer_accesses", "admin_users"
  add_foreign_key "admin_user_customer_accesses", "customers"
  add_foreign_key "agreements_agreement_types", "attachments", column: "document_template_id"
  add_foreign_key "agreements_simple_agreement_memberships", "agreements_simple_agreements", column: "simple_agreement_id"
  add_foreign_key "agreements_simple_agreement_memberships", "tenants"
  add_foreign_key "agreements_simple_agreements", "agreements_agreement_types", column: "agreement_type_id"
  add_foreign_key "agreements_simple_agreements", "companies"
  add_foreign_key "agreements_simple_agreements", "properties"
  add_foreign_key "alliance_transactions", "payments"
  add_foreign_key "api_v2_keys", "property_managers", column: "user_id"
  add_foreign_key "api_v2_owner_statement_entries", "companies", column: "entity_id"
  add_foreign_key "api_v2_owner_statement_entries", "properties"
  add_foreign_key "approvals_approvals", "approvals_requests", column: "request_id"
  add_foreign_key "approvals_approvals", "approvals_rules", column: "rule_id"
  add_foreign_key "approvals_change_requests", "property_managers", column: "created_by_id"
  add_foreign_key "approvals_change_requests", "property_managers", column: "requested_from_id"
  add_foreign_key "approvals_rules", "approvals_rules", column: "dependency_id"
  add_foreign_key "background_checks", "lease_application_memberships"
  add_foreign_key "bank_account_funds_transfers", "bank_accounts"
  add_foreign_key "bank_account_funds_transfers", "property_managers", column: "processed_by_id"
  add_foreign_key "bank_account_reconciliations", "bank_accounts"
  add_foreign_key "bank_account_reconciliations", "property_managers", column: "submitted_by_id"
  add_foreign_key "bank_accounts", "plutus_accounts", column: "ledger_account_id"
  add_foreign_key "billings", "invoices"
  add_foreign_key "billings", "maintenance_estimates", column: "estimate_id"
  add_foreign_key "billings", "projects"
  add_foreign_key "broadcasts_recipients", "broadcasts"
  add_foreign_key "budget_amounts", "budgets"
  add_foreign_key "budget_amounts", "projects"
  add_foreign_key "budgets", "companies"
  add_foreign_key "budgets", "properties"
  add_foreign_key "buildings", "properties"
  add_foreign_key "calendar_events", "property_managers", column: "author_id"
  add_foreign_key "cash_pay_cards", "tenants"
  add_foreign_key "charge_presets", "configurations"
  add_foreign_key "charge_presets", "plutus_accounts", column: "account_id"
  add_foreign_key "charge_presets_late_fees", "charge_presets"
  add_foreign_key "charge_presets_late_fees", "charge_presets", column: "late_fee_id"
  add_foreign_key "charge_schedule_entries", "charge_presets"
  add_foreign_key "charge_schedule_entries", "plutus_accounts", column: "account_id"
  add_foreign_key "charge_schedule_entry_allocations", "charge_schedule_entries", column: "entry_id"
  add_foreign_key "charge_schedule_entry_allocations", "lease_memberships"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "clearing_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "default_maintenance_expense_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "default_maintenance_revenue_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "default_management_fees_expense_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "default_management_fees_revenue_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "default_management_leasing_commissions_expense_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "default_management_leasing_commissions_revenue_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "due_from_client_entity_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "due_to_customer_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "management_held_security_deposit_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "merchant_processing_revenue_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "owner_cash_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "prepaid_expense_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "prepaid_revenue_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "property_transfers_account_id"
  add_foreign_key "charts_of_accounts", "plutus_accounts", column: "security_deposit_account_id"
  add_foreign_key "collections_communications", "tenants"
  add_foreign_key "collections_demand_letter_batches", "property_managers", column: "created_by_id"
  add_foreign_key "collections_demand_letters", "collections_demand_letter_batches", column: "demand_letter_batch_id"
  add_foreign_key "collections_demand_letters", "leases"
  add_foreign_key "collections_evictions", "leases"
  add_foreign_key "collections_evictions", "property_managers", column: "created_by_id"
  add_foreign_key "collections_evictions", "vendors", column: "assigned_vendor_id"
  add_foreign_key "collections_evictions", "vendors", column: "court_id"
  add_foreign_key "collections_opt_outs", "tenants"
  add_foreign_key "companies", "charts_of_accounts"
  add_foreign_key "companies", "plutus_accounts", column: "default_deposit_account_id"
  add_foreign_key "companies", "plutus_accounts", column: "default_withdrawal_account_id"
  add_foreign_key "companies", "portfolios"
  add_foreign_key "configurations", "bank_accounts", column: "application_deposit_account_id"
  add_foreign_key "configurations", "charge_presets", column: "demand_letter_owner_charge_preset_id"
  add_foreign_key "configurations", "charge_presets", column: "demand_letter_tenant_charge_preset_id"
  add_foreign_key "configurations", "charge_presets", column: "eviction_owner_charge_preset_id"
  add_foreign_key "configurations", "charge_presets", column: "eviction_tenant_charge_preset_id"
  add_foreign_key "configurations", "charge_presets", column: "month_to_month_charge_preset_id"
  add_foreign_key "configurations", "charge_presets", column: "nsf_charge_preset_id"
  add_foreign_key "configurations", "charge_presets", column: "reservation_charge_preset_id"
  add_foreign_key "configurations", "charts_of_accounts"
  add_foreign_key "configurations", "inspection_templates", column: "move_in_inspection_template_id"
  add_foreign_key "configurations", "inspection_templates", column: "move_out_inspection_template_id"
  add_foreign_key "credit_presets", "configurations"
  add_foreign_key "credit_presets", "plutus_accounts", column: "account_id"
  add_foreign_key "custom_forms_automation_settings_event_date_time_v1s", "custom_forms_forms", column: "form_id"
  add_foreign_key "custom_forms_automation_settings_payment_v1s", "bank_accounts"
  add_foreign_key "custom_forms_automation_settings_payment_v1s", "custom_forms_forms", column: "form_id"
  add_foreign_key "custom_forms_automation_settings_payment_v1s", "plutus_accounts", column: "receivable_account_id"
  add_foreign_key "custom_forms_form_field_answers", "custom_forms_form_fields", column: "form_field_id"
  add_foreign_key "custom_forms_form_field_answers", "custom_forms_submissions", column: "submission_id"
  add_foreign_key "custom_forms_form_fields", "custom_forms_forms", column: "form_id"
  add_foreign_key "custom_forms_submissions", "custom_forms_forms", column: "form_id"
  add_foreign_key "customers", "enterprises"
  add_foreign_key "data_imports", "property_managers", column: "uploaded_by_id"
  add_foreign_key "deposit_batches", "bank_account_reconciliations", column: "reconciliation_id"
  add_foreign_key "deposit_batches", "bank_accounts"
  add_foreign_key "financing_refinance_invites", "owners"
  add_foreign_key "financing_refinance_invites", "properties"
  add_foreign_key "financing_refinance_invites", "property_managers", column: "created_by_id"
  add_foreign_key "financing_refinance_requests", "financing_refinance_invites", column: "invite_id"
  add_foreign_key "floorplans", "properties"
  add_foreign_key "guest_cards", "portfolios"
  add_foreign_key "hap_contracts", "properties"
  add_foreign_key "inspectify_orders", "inspection_reports"
  add_foreign_key "inspectify_vendor_tokens", "vendors"
  add_foreign_key "inspection_activities", "inspection_reports"
  add_foreign_key "inspection_punch_list_entries", "inspection_responses", column: "response_id"
  add_foreign_key "inspection_punch_list_entries", "sku_list_items"
  add_foreign_key "inspection_questions", "inspection_templates", column: "template_id"
  add_foreign_key "inspection_records", "inspection_records", column: "parent_id"
  add_foreign_key "inspection_records", "inspection_reports", column: "report_id"
  add_foreign_key "inspection_reports", "inspection_templates", column: "template_id"
  add_foreign_key "inspection_reports", "properties"
  add_foreign_key "inspection_reports", "property_managers", column: "opened_by_id"
  add_foreign_key "inspection_reports", "tasks"
  add_foreign_key "inspection_responses", "inspection_questions", column: "question_id"
  add_foreign_key "inspection_responses", "inspection_records", column: "record_id"
  add_foreign_key "inspection_responses", "rooms"
  add_foreign_key "invoice_payments", "invoices"
  add_foreign_key "invoice_payments", "payments"
  add_foreign_key "invoice_processing_associated_attachments", "attachments"
  add_foreign_key "invoice_processing_associated_attachments", "invoices"
  add_foreign_key "invoices", "invoices", column: "forwarded_from_id"
  add_foreign_key "invoices", "lease_memberships", column: "buyer_lease_membership_id"
  add_foreign_key "invoices", "lease_memberships", column: "seller_lease_membership_id"
  add_foreign_key "invoices", "lending_loans", column: "loan_id"
  add_foreign_key "invoices", "units", column: "buyer_unit_id"
  add_foreign_key "invoices", "units", column: "seller_unit_id"
  add_foreign_key "itemized_damages", "lease_memberships"
  add_foreign_key "itemized_damages", "lease_move_outs", column: "move_out_id"
  add_foreign_key "labor_items", "line_items"
  add_foreign_key "labor_items", "maintenance_tickets"
  add_foreign_key "labor_items", "property_managers", column: "employee_id"
  add_foreign_key "landlord_verifications", "lease_application_residences"
  add_foreign_key "lease_addendums", "leases"
  add_foreign_key "lease_application_contacts", "lease_applications"
  add_foreign_key "lease_application_credit_references", "lease_applications"
  add_foreign_key "lease_application_custom_question_responses", "lease_application_custom_questions", column: "custom_question_id"
  add_foreign_key "lease_application_custom_question_responses", "lease_applications"
  add_foreign_key "lease_application_custom_questions", "configurations"
  add_foreign_key "lease_application_income_sources", "lease_applications"
  add_foreign_key "lease_application_memberships", "lease_applications"
  add_foreign_key "lease_application_memberships", "tenants"
  add_foreign_key "lease_application_residences", "lease_applications"
  add_foreign_key "lease_applications", "floorplans"
  add_foreign_key "lease_applications", "lease_terms"
  add_foreign_key "lease_applications", "property_managers", column: "adjudicated_by_id"
  add_foreign_key "lease_applications", "tenants", column: "lead_id"
  add_foreign_key "lease_applications", "units"
  add_foreign_key "lease_move_out_custom_damages", "lease_move_outs", column: "move_out_id"
  add_foreign_key "lease_move_out_custom_damages", "plutus_accounts", column: "account_id"
  add_foreign_key "lease_move_out_memberships", "lease_memberships"
  add_foreign_key "lease_move_out_memberships", "lease_move_outs", column: "move_out_id"
  add_foreign_key "lease_move_outs", "inspection_reports", column: "inspection_id"
  add_foreign_key "lease_move_outs", "leases"
  add_foreign_key "lease_move_outs", "maintenance_tickets", column: "rekey_maintenance_ticket_id"
  add_foreign_key "lease_notice_of_non_renewals", "leases"
  add_foreign_key "lease_pet_memberships", "leases"
  add_foreign_key "lease_pet_memberships", "pets"
  add_foreign_key "lease_terms", "configurations"
  add_foreign_key "leases", "leases", column: "renewal_id"
  add_foreign_key "lending_loan_installment_adjustments", "lending_loans", column: "loan_id"
  add_foreign_key "lending_loans", "companies", column: "borrower_id"
  add_foreign_key "lending_loans", "companies", column: "lender_id"
  add_foreign_key "lending_loans", "properties"
  add_foreign_key "line_item_markups", "line_items", column: "destination_item_id"
  add_foreign_key "line_item_markups", "line_items", column: "source_item_id"
  add_foreign_key "line_items", "charge_presets"
  add_foreign_key "line_items", "invoices"
  add_foreign_key "line_items", "plutus_accounts", column: "payable_account_id"
  add_foreign_key "line_items", "plutus_accounts", column: "receivable_account_id"
  add_foreign_key "linkages", "invoices"
  add_foreign_key "linkages", "maintenance_tickets"
  add_foreign_key "linkages", "projects"
  add_foreign_key "listing_publish_events", "listings"
  add_foreign_key "listings", "floorplans"
  add_foreign_key "listings", "property_managers", column: "agent_id"
  add_foreign_key "maintenance_bid_items", "maintenance_bids", column: "bid_id"
  add_foreign_key "maintenance_bid_request_invites", "maintenance_bid_requests", column: "request_id"
  add_foreign_key "maintenance_bid_request_invites", "vendors"
  add_foreign_key "maintenance_bid_requests", "maintenance_tickets"
  add_foreign_key "maintenance_bid_requests", "property_managers", column: "requested_by_id"
  add_foreign_key "maintenance_bid_requests", "vendors"
  add_foreign_key "maintenance_bids", "maintenance_bid_request_invites", column: "invite_id"
  add_foreign_key "maintenance_bids", "maintenance_bid_requests", column: "request_id"
  add_foreign_key "maintenance_estimate_areas", "maintenance_estimate_sections", column: "section_id"
  add_foreign_key "maintenance_estimate_sections", "maintenance_estimates", column: "estimate_id"
  add_foreign_key "maintenance_estimate_tasks", "inspection_questions", column: "question_id"
  add_foreign_key "maintenance_estimate_tasks", "inspection_records", column: "record_id"
  add_foreign_key "maintenance_estimate_tasks", "maintenance_estimate_areas", column: "area_id"
  add_foreign_key "maintenance_estimates", "companies", column: "bill_from_id"
  add_foreign_key "maintenance_estimates", "companies", column: "bill_to_id"
  add_foreign_key "maintenance_estimates", "inspection_reports", column: "inspection_id"
  add_foreign_key "maintenance_estimates", "maintenance_estimates", column: "copied_from_id"
  add_foreign_key "maintenance_estimates", "maintenance_tickets"
  add_foreign_key "maintenance_estimates", "properties"
  add_foreign_key "maintenance_estimates", "property_managers", column: "prepared_by_id"
  add_foreign_key "maintenance_estimates", "units"
  add_foreign_key "maintenance_surveys", "maintenance_tickets"
  add_foreign_key "maintenance_surveys", "tenants"
  add_foreign_key "maintenance_ticket_appointments", "maintenance_tickets"
  add_foreign_key "maintenance_ticket_appointments", "property_managers", column: "created_by_id"
  add_foreign_key "maintenance_ticket_appointments", "property_managers", column: "employee_id"
  add_foreign_key "maintenance_ticket_appointments", "vendors"
  add_foreign_key "maintenance_ticket_defers", "maintenance_tickets"
  add_foreign_key "maintenance_ticket_defers", "property_managers", column: "created_by_id"
  add_foreign_key "maintenance_ticket_events", "invoices"
  add_foreign_key "maintenance_ticket_events", "maintenance_bids", column: "bid_id"
  add_foreign_key "maintenance_ticket_events", "maintenance_estimates", column: "estimate_id"
  add_foreign_key "maintenance_ticket_events", "maintenance_tickets"
  add_foreign_key "maintenance_ticket_events", "vendors", column: "vendor_assignee_id"
  add_foreign_key "maintenance_tickets", "leases"
  add_foreign_key "maintenance_tickets", "property_managers", column: "assessed_by_id"
  add_foreign_key "maintenance_tickets", "units"
  add_foreign_key "maintenance_work_performed_statement_items", "accounting_statements", column: "statement_id"
  add_foreign_key "maintenance_work_performed_statement_items", "maintenance_tickets"
  add_foreign_key "management_contract_account_settings", "management_contracts"
  add_foreign_key "management_contract_account_settings", "plutus_accounts", column: "account_id"
  add_foreign_key "management_contract_memberships", "management_contracts"
  add_foreign_key "management_contract_memberships", "properties"
  add_foreign_key "management_contracts", "companies"
  add_foreign_key "member_onboarding_assignments", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_assignments", "tenants"
  add_foreign_key "member_onboarding_charge_memberships", "charge_presets"
  add_foreign_key "member_onboarding_charge_memberships", "member_onboarding_charges", column: "charge_id"
  add_foreign_key "member_onboarding_charges", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_completions", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_completions", "tenants"
  add_foreign_key "member_onboarding_configurations", "portfolios"
  add_foreign_key "member_onboarding_guarantors", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_information_collections", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_lease_agreements", "attachments", column: "lease_template_id"
  add_foreign_key "member_onboarding_lease_agreements", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_lease_agreements", "property_managers", column: "countersigner_id"
  add_foreign_key "member_onboarding_membership_agreements", "attachments", column: "membership_template_id"
  add_foreign_key "member_onboarding_membership_agreements", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_membership_agreements", "property_managers", column: "countersigner_id"
  add_foreign_key "member_onboarding_property_memberships", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_property_memberships", "properties"
  add_foreign_key "member_onboarding_risk_release_installments", "member_onboarding_risk_releases", column: "risk_release_configuration_id"
  add_foreign_key "member_onboarding_risk_releases", "member_onboarding_configurations", column: "configuration_id"
  add_foreign_key "member_onboarding_risk_releases", "plutus_accounts", column: "account_id"
  add_foreign_key "merchant_accounts", "bank_accounts"
  add_foreign_key "mercury_orders", "inspection_reports", column: "inspection_id"
  add_foreign_key "messaging_contact_group_activities", "messaging_contact_groups", column: "contact_group_id"
  add_foreign_key "messaging_contact_group_memberships", "messaging_contact_groups", column: "contact_group_id"
  add_foreign_key "messaging_emails", "messaging_emails", column: "replying_to_id"
  add_foreign_key "messaging_message_deliveries", "messaging_emails", column: "message_id"
  add_foreign_key "mobile_devices", "property_managers", column: "user_id"
  add_foreign_key "notifications", "attachments"
  add_foreign_key "oauth_access_grants", "property_managers", column: "resource_owner_id"
  add_foreign_key "oauth_access_tokens", "property_managers", column: "resource_owner_id"
  add_foreign_key "owner_contribution_allocations", "owner_contributions"
  add_foreign_key "owner_contribution_allocations", "properties"
  add_foreign_key "owner_contribution_requests", "owners"
  add_foreign_key "owner_contribution_requests", "properties"
  add_foreign_key "owner_contribution_requests", "property_managers", column: "requested_by_id"
  add_foreign_key "owner_contributions", "companies"
  add_foreign_key "owner_contributions", "owner_contribution_requests", column: "request_id"
  add_foreign_key "owner_invites", "property_managers", column: "invited_by_id"
  add_foreign_key "ownerships", "companies", column: "entity_id"
  add_foreign_key "parking_allocations", "leases"
  add_foreign_key "parking_lots", "properties"
  add_foreign_key "parking_reservations", "leases"
  add_foreign_key "parking_reservations", "parking_spaces"
  add_foreign_key "parking_spaces", "parking_lots"
  add_foreign_key "pay_lease_transactions", "payments"
  add_foreign_key "payment_plan_installments", "payment_plans"
  add_foreign_key "payment_plan_installments", "scheduled_payments"
  add_foreign_key "payment_plan_invoice_memberships", "invoices"
  add_foreign_key "payment_plan_invoice_memberships", "payment_plans"
  add_foreign_key "payment_plan_preset_installments", "payment_plan_presets", column: "preset_id"
  add_foreign_key "payment_plan_presets", "configurations"
  add_foreign_key "payment_plans", "agreements_simple_agreements", column: "simple_agreement_id"
  add_foreign_key "payment_plans", "lease_memberships"
  add_foreign_key "payment_plans", "payment_plan_presets", column: "preset_id"
  add_foreign_key "payments", "bank_accounts", column: "credit_bank_account_id"
  add_foreign_key "payments", "bank_accounts", column: "debit_bank_account_id"
  add_foreign_key "payments", "lease_memberships", column: "payee_lease_membership_id"
  add_foreign_key "payments", "lease_memberships", column: "payer_lease_membership_id"
  add_foreign_key "payments", "lending_loans", column: "loan_id"
  add_foreign_key "payments", "plutus_accounts", column: "payable_credit_note_account_id"
  add_foreign_key "payments", "plutus_accounts", column: "receivable_credit_note_account_id"
  add_foreign_key "payments", "units", column: "payee_unit_id"
  add_foreign_key "payments", "units", column: "payer_unit_id"
  add_foreign_key "pets", "lease_applications"
  add_foreign_key "plutus_amounts", "bank_account_reconciliations", column: "reconciliation_id"
  add_foreign_key "plutus_amounts", "deposit_batches"
  add_foreign_key "plutus_entries", "recurring_schedules"
  add_foreign_key "plutus_entry_automatic_reversals", "plutus_entries", column: "destination_entry_id"
  add_foreign_key "plutus_entry_automatic_reversals", "plutus_entries", column: "source_entry_id"
  add_foreign_key "plutus_entry_linkages", "plutus_entries", column: "left_entry_id"
  add_foreign_key "plutus_entry_linkages", "plutus_entries", column: "right_entry_id"
  add_foreign_key "portfolios", "configurations"
  add_foreign_key "portfolios_bank_accounts", "bank_accounts"
  add_foreign_key "portfolios_bank_accounts", "portfolios"
  add_foreign_key "profit_stars_transactions", "merchant_accounts"
  add_foreign_key "profit_stars_transactions", "payments"
  add_foreign_key "project_bids", "tasks"
  add_foreign_key "project_bids", "vendors"
  add_foreign_key "project_board_columns", "project_boards", column: "board_id"
  add_foreign_key "project_dependencies", "tasks"
  add_foreign_key "project_memberships", "projects"
  add_foreign_key "project_phases", "projects"
  add_foreign_key "projects", "maintenance_estimates", column: "estimate_id"
  add_foreign_key "projects", "project_board_columns", column: "column_id"
  add_foreign_key "projects", "project_boards", column: "board_id"
  add_foreign_key "projects_tickets", "maintenance_tickets"
  add_foreign_key "projects_tickets", "projects"
  add_foreign_key "properties", "configurations"
  add_foreign_key "properties", "vendors", column: "electric_utility_id"
  add_foreign_key "properties", "vendors", column: "gas_utility_id"
  add_foreign_key "properties", "vendors", column: "recycling_utility_id"
  add_foreign_key "properties", "vendors", column: "sewer_utility_id"
  add_foreign_key "properties", "vendors", column: "trash_utility_id"
  add_foreign_key "properties", "vendors", column: "water_utility_id"
  add_foreign_key "property_analyses", "properties"
  add_foreign_key "property_managers", "roles"
  add_foreign_key "property_parcels", "properties"
  add_foreign_key "property_transfers", "properties", column: "destination_property_id"
  add_foreign_key "property_transfers", "properties", column: "source_property_id"
  add_foreign_key "rbac_permissions", "roles"
  add_foreign_key "reams_orders", "inspection_reports", column: "inspection_id"
  add_foreign_key "reports_email_schedules", "property_managers", column: "created_by_id"
  add_foreign_key "reports_packet_template_entries", "reports_packet_templates", column: "packet_template_id"
  add_foreign_key "risk_release_enrollments", "agreements_simple_agreement_memberships", column: "simple_agreement_membership_id"
  add_foreign_key "risk_release_enrollments", "lease_memberships"
  add_foreign_key "rooms", "properties"
  add_foreign_key "rooms", "units"
  add_foreign_key "saferent_screenings", "lease_applications"
  add_foreign_key "scheduled_payments", "agreements_simple_agreements", column: "simple_agreement_id"
  add_foreign_key "scheduled_payments", "lease_memberships"
  add_foreign_key "scheduled_payments", "properties"
  add_foreign_key "sierra_leone_inspection_orders", "inspection_reports"
  add_foreign_key "sierra_leone_vendor_tokens", "vendors"
  add_foreign_key "sku_list_items", "vendors"
  add_foreign_key "syndication_listing_supplements", "listings"
  add_foreign_key "syndication_property_supplements", "zillow_claims"
  add_foreign_key "syndication_supplements", "listings"
  add_foreign_key "tasks", "projects"
  add_foreign_key "tasks", "property_managers", column: "approval_requested_by_id"
  add_foreign_key "tasks", "property_managers", column: "approved_by_id"
  add_foreign_key "tasks", "roles", column: "approving_role_id"
  add_foreign_key "taxes_irs_filings", "companies", column: "payer_id"
  add_foreign_key "taxes_nelco_accounts", "property_managers", column: "submitted_by_id"
  add_foreign_key "taxes_nelco_batches", "property_managers", column: "created_by_id"
  add_foreign_key "taxes_nelco_batches", "property_managers", column: "submitted_by_id"
  add_foreign_key "taxes_nelco_batches", "taxes_batch_groups"
  add_foreign_key "taxes_nelco_submissions", "taxes_irs_filings"
  add_foreign_key "taxes_nelco_submissions", "taxes_nelco_batches"
  add_foreign_key "tenants", "property_managers", column: "leasing_agent_id"
  add_foreign_key "the_closing_docs_screening_groups", "lease_applications"
  add_foreign_key "tours", "guest_cards"
  add_foreign_key "tours", "properties"
  add_foreign_key "tours", "property_managers", column: "tour_guide_id"
  add_foreign_key "tours", "sources"
  add_foreign_key "tours", "tenants", column: "lead_id"
  add_foreign_key "tunisia_application_forms", "companies"
  add_foreign_key "tunisia_application_forms", "property_managers", column: "created_by_id"
  add_foreign_key "tunisia_authorized_users", "companies"
  add_foreign_key "tunisia_check_payment_additional_verifications", "tunisia_deposit_accounts", column: "deposit_account_id"
  add_foreign_key "tunisia_deposit_accounts", "bank_accounts"
  add_foreign_key "tunisia_deposit_accounts", "property_managers", column: "opened_by_id"
  add_foreign_key "unit_downtimes", "units"
  add_foreign_key "unit_reservations", "tenants"
  add_foreign_key "unit_reservations", "units"
  add_foreign_key "units", "buildings"
  add_foreign_key "units", "floorplans"
  add_foreign_key "user_invites", "customers"
  add_foreign_key "user_login_fingerprints", "customers"
  add_foreign_key "user_profiles", "customers"
  add_foreign_key "user_profiles", "user_accounts", column: "account_id"
  add_foreign_key "utilities_transfers", "properties", column: "service_location_id"
  add_foreign_key "utilities_transfers", "tenants"
  add_foreign_key "utilities_transfers", "vendors", column: "utility_id"
  add_foreign_key "vehicles", "lease_applications"
  add_foreign_key "vendor_assignments", "maintenance_tickets"
  add_foreign_key "vendor_assignments", "projects"
  add_foreign_key "vendor_assignments", "vendors"
  add_foreign_key "vendor_contract_property_memberships", "properties"
  add_foreign_key "vendor_contract_property_memberships", "vendor_contracts", column: "contract_id"
  add_foreign_key "vendor_contracts", "companies", column: "entity_id"
  add_foreign_key "vendor_contracts", "vendors"
  add_foreign_key "vendor_invites", "vendors"
  add_foreign_key "waitlist_entries", "floorplans"
  add_foreign_key "waitlist_entries", "lease_applications"
  add_foreign_key "zeamster_transactions", "payments"
  add_foreign_key "zillow_claims", "property_managers", column: "agent_id"
  add_foreign_key "zillow_claims_properties", "properties"
  add_foreign_key "zillow_claims_properties", "zillow_claims"

  create_view "lease_membership_balances", sql_definition: <<-SQL
      SELECT plutus_entries.lease_membership_id,
      sum(
          CASE
              WHEN ((plutus_amounts.type)::text = 'Plutus::DebitAmount'::text) THEN plutus_amounts.amount
              ELSE (- plutus_amounts.amount)
          END) AS amount_cents
     FROM ((plutus_amounts
       LEFT JOIN plutus_entries ON ((plutus_amounts.entry_id = plutus_entries.id)))
       LEFT JOIN plutus_accounts ON ((plutus_amounts.account_id = plutus_accounts.id)))
    WHERE ((plutus_accounts.name)::text = 'Accounts Receivable'::text)
    GROUP BY plutus_entries.lease_membership_id;
  SQL
  create_view "project_budget_entries", sql_definition: <<-SQL
      WITH project_expenditures AS (
           SELECT linkages.project_id,
              plutus_accounts.id AS account_id,
              sum(
                  CASE plutus_amounts.type
                      WHEN 'Plutus::DebitAmount'::text THEN plutus_amounts.amount
                      ELSE (- plutus_amounts.amount)
                  END) AS actual_cents
             FROM ((((invoices
               JOIN linkages ON ((linkages.invoice_id = invoices.id)))
               JOIN plutus_entries ON (((plutus_entries.commercial_document_id = invoices.id) AND ((plutus_entries.commercial_document_type)::text = 'Invoice'::text))))
               JOIN plutus_amounts ON ((plutus_amounts.entry_id = plutus_entries.id)))
               JOIN plutus_accounts ON (((plutus_accounts.id = plutus_amounts.account_id) AND ((plutus_accounts.type)::text = 'Plutus::Expense'::text))))
            GROUP BY linkages.project_id, plutus_accounts.id
          )
   SELECT COALESCE(project_expenditures.project_id, budget_amounts.project_id) AS project_id,
      COALESCE(project_expenditures.account_id, budget_amounts.account_id) AS account_id,
      COALESCE(project_expenditures.actual_cents, (0)::numeric) AS actual_cents,
      COALESCE(budget_amounts.amount_cents, 0) AS budgeted_cents
     FROM (project_expenditures
       FULL JOIN budget_amounts ON (((project_expenditures.project_id = budget_amounts.project_id) AND (project_expenditures.account_id = budget_amounts.account_id))));
  SQL
  create_view "activities", sql_definition: <<-SQL
      WITH assigned_tasks AS (
           SELECT assignments.user_id,
              (((('/operations/projects/'::text || tasks.project_id) || '/tasks/'::text) || tasks.id))::character varying AS path,
              'task'::character varying AS type,
              tasks.name AS title,
              tasks.description,
              tasks.created_at,
              (tasks.due_date)::timestamp with time zone AS due_at,
              NULL::timestamp with time zone AS scheduled_at
             FROM (assignments
               JOIN tasks ON ((((assignments.assignable_type)::text = 'Task'::text) AND (assignments.assignable_id = tasks.id) AND (tasks.completed_at IS NULL))))
          ), assigned_work_orders AS (
           SELECT assignments.user_id,
              (('/maintenance/tickets/'::text || maintenance_tickets.id))::character varying AS path,
              'work_order'::character varying AS type,
              maintenance_tickets.subject AS title,
              maintenance_tickets.description,
              maintenance_tickets.created_at,
              NULL::timestamp with time zone AS due_at,
              NULL::timestamp with time zone AS scheduled_at
             FROM (assignments
               JOIN maintenance_tickets ON ((((assignments.assignable_type)::text = 'MaintenanceTicket'::text) AND (assignments.assignable_id = maintenance_tickets.id) AND (maintenance_tickets.closed_at IS NULL))))
          ), scheduled_tours AS (
           SELECT tours.tour_guide_id AS user_id,
              (('/leasing/guest_cards/'::text || tours.guest_card_id))::character varying AS path,
              'tour'::character varying AS type,
              ('Tour of '::text || (properties.name)::text) AS title,
              ((('With '::text || (tenants.first_name)::text) || ' '::text) || (tenants.last_name)::text) AS description,
              tours.created_at,
              NULL::timestamp with time zone AS due_at,
              tours."time" AS scheduled_at
             FROM (((tours
               JOIN guest_cards ON ((guest_cards.id = tours.guest_card_id)))
               JOIN tenants ON ((tenants.id = guest_cards.tenant_id)))
               LEFT JOIN properties ON ((properties.id = guest_cards.property_id)))
            WHERE (tours."time" > now())
          )
   SELECT assigned_tasks.user_id,
      assigned_tasks.path,
      assigned_tasks.type,
      assigned_tasks.title,
      assigned_tasks.description,
      assigned_tasks.created_at,
      assigned_tasks.due_at,
      assigned_tasks.scheduled_at
     FROM assigned_tasks
  UNION ALL
   SELECT assigned_work_orders.user_id,
      assigned_work_orders.path,
      assigned_work_orders.type,
      assigned_work_orders.title,
      assigned_work_orders.description,
      assigned_work_orders.created_at,
      assigned_work_orders.due_at,
      assigned_work_orders.scheduled_at
     FROM assigned_work_orders
  UNION ALL
   SELECT scheduled_tours.user_id,
      scheduled_tours.path,
      scheduled_tours.type,
      scheduled_tours.title,
      scheduled_tours.description,
      scheduled_tours.created_at,
      scheduled_tours.due_at,
      scheduled_tours.scheduled_at
     FROM scheduled_tours;
  SQL
  create_view "unit_availabilities", sql_definition: <<-SQL
      WITH lease_ranges AS (
           SELECT leases.unit_id,
              leases.start_date,
                  CASE leases.kind
                      WHEN 0 THEN leases.end_date
                      ELSE 'infinity'::date
                  END AS end_date
             FROM leases
            WHERE (leases.archived_at IS NULL)
          ), downtime_ranges AS (
           SELECT unit_downtimes.unit_id,
              unit_downtimes.start_date,
              COALESCE(unit_downtimes.end_date, 'infinity'::date) AS end_date
             FROM unit_downtimes
          ), ranges AS (
           SELECT lease_ranges.unit_id,
              lease_ranges.start_date,
              lease_ranges.end_date
             FROM lease_ranges
          UNION ALL
           SELECT downtime_ranges.unit_id,
              downtime_ranges.start_date,
              downtime_ranges.end_date
             FROM downtime_ranges
          ), before_first AS (
           SELECT ranges.unit_id,
              '-infinity'::date AS range_start,
              min(ranges.start_date) AS range_end
             FROM ranges
            GROUP BY ranges.unit_id
          ), after_last AS (
           SELECT ranges.unit_id,
              max(ranges.end_date) AS range_start,
              'infinity'::date AS range_end
             FROM ranges
            GROUP BY ranges.unit_id
          ), lease_gaps AS (
           SELECT t.unit_id,
              t.range_start,
              t.range_end
             FROM ( SELECT before_first.unit_id,
                      before_first.range_start,
                      before_first.range_end
                     FROM before_first
                  UNION
                   SELECT ranges.unit_id,
                      max(ranges.end_date) OVER win AS max,
                      lead(ranges.start_date) OVER win AS lead
                     FROM ranges
                    WINDOW win AS (PARTITION BY ranges.unit_id ORDER BY ranges.start_date)
                  UNION
                   SELECT after_last.unit_id,
                      after_last.range_start,
                      after_last.range_end
                     FROM after_last) t
            WHERE (t.range_start < t.range_end)
          )
   SELECT units.id AS unit_id,
      COALESCE(lease_gaps.range_start, '-infinity'::date) AS start_date,
      COALESCE(lease_gaps.range_end, 'infinity'::date) AS end_date,
          CASE
              WHEN (COALESCE(lease_gaps.range_start, '-infinity'::date) = '-infinity'::date) THEN 'Infinity'::double precision
              WHEN (COALESCE(lease_gaps.range_end, 'infinity'::date) = 'infinity'::date) THEN 'Infinity'::double precision
              ELSE ((lease_gaps.range_end - lease_gaps.range_start))::double precision
          END AS duration
     FROM (units
       LEFT JOIN lease_gaps ON ((lease_gaps.unit_id = units.id)))
    ORDER BY units.id, lease_gaps.range_start;
  SQL
  create_view "ledger_balance_entries", sql_definition: <<-SQL
      WITH accounts_receivable_ids AS (
           SELECT charts_of_accounts.id AS chart_of_accounts_id,
                  CASE
                      WHEN (charts_of_accounts.accounts_receivable_id IS NULL) THEN ( SELECT plutus_accounts_1.id
                         FROM plutus_accounts plutus_accounts_1
                        WHERE (((plutus_accounts_1.name)::text = 'Accounts Receivable'::text) AND (plutus_accounts_1.tenant_id = charts_of_accounts.id)))
                      ELSE charts_of_accounts.accounts_receivable_id
                  END AS accounts_receivable_id
             FROM charts_of_accounts
            GROUP BY charts_of_accounts.id
          )
   SELECT plutus_entries.id,
      plutus_entries.description,
      plutus_entries.date,
      plutus_entries.commercial_document_id,
      plutus_entries.commercial_document_type,
      plutus_entries.created_at,
      plutus_entries.updated_at,
      plutus_entries.property_id,
      plutus_entries.lease_membership_id,
      plutus_entries.journal_id,
      plutus_entries.unit_id,
      plutus_entries.tenant_id,
          CASE
              WHEN ((plutus_amounts.type)::text = 'Plutus::DebitAmount'::text) THEN plutus_amounts.amount
              ELSE (- plutus_amounts.amount)
          END AS amount_cents
     FROM ((plutus_amounts
       LEFT JOIN plutus_accounts ON ((plutus_accounts.id = plutus_amounts.account_id)))
       LEFT JOIN plutus_entries ON ((plutus_entries.id = plutus_amounts.entry_id)))
    WHERE (plutus_amounts.account_id IN ( SELECT accounts_receivable_ids.accounts_receivable_id
             FROM accounts_receivable_ids));
  SQL
  create_view "lease_chains", sql_definition: <<-SQL
      WITH RECURSIVE t(head_id, id, count, lease_ids) AS (
           SELECT leases.id AS head_id,
              leases.id,
              1 AS count,
              ARRAY[leases.id] AS lease_ids
             FROM leases
            WHERE ((leases.renewal_id IS NULL) AND (leases.archived_at IS NULL))
          UNION ALL
           SELECT rec.head_id,
              leases.id,
              (rec.count + 1) AS count,
              (rec.lease_ids || leases.id) AS lease_ids
             FROM leases,
              t rec
            WHERE ((leases.renewal_id = rec.id) AND (leases.archived_at IS NULL))
          ), chains AS (
           SELECT t.head_id,
              t.id,
              t.count,
              t.lease_ids,
              t.maxlevel
             FROM ( SELECT t_1.head_id,
                      t_1.id,
                      t_1.count,
                      t_1.lease_ids,
                      max(t_1.count) OVER (PARTITION BY t_1.head_id) AS maxlevel
                     FROM t t_1) t
            WHERE (t.count = t.maxlevel)
          ), chained_leases AS (
           SELECT leases.id,
              leases.updated_at,
              leases.start_date,
              leases.end_date,
              leases.unit_id,
              chains_1.id AS chain_id
             FROM (leases
               JOIN chains chains_1 ON ((leases.id = ANY (chains_1.lease_ids))))
          )
   SELECT chains.id,
      chains.count,
      chains.lease_ids,
      min(chained_leases.start_date) AS start_date,
      max(chained_leases.end_date) AS end_date,
      min(chained_leases.unit_id) AS current_unit_id,
      array_agg(lease_memberships.id) AS lease_membership_ids,
      max(chained_leases.updated_at) AS updated_at
     FROM ((chains
       JOIN chained_leases ON ((chained_leases.chain_id = chains.id)))
       JOIN lease_memberships ON ((lease_memberships.lease_id = chained_leases.id)))
    GROUP BY chains.id, chains.count, chains.lease_ids;
  SQL
  create_view "lease_chain_memberships", sql_definition: <<-SQL
      SELECT leases.id AS lease_id,
      lease_chains.id AS chain_id
     FROM (leases
       JOIN lease_chains ON ((leases.id = ANY (lease_chains.lease_ids))));
  SQL
  create_view "invoice_total_payments", sql_definition: <<-SQL
      SELECT invoices.id AS invoice_id,
      COALESCE(sum(
          CASE
              WHEN (invoice_payments.reversed_at IS NOT NULL) THEN 0
              ELSE invoice_payments.amount_cents
          END), (0)::bigint) AS payment
     FROM (invoices
       LEFT JOIN invoice_payments ON ((invoice_payments.invoice_id = invoices.id)))
    GROUP BY invoices.id;
  SQL
  create_view "unapplied_payment_activities", sql_definition: <<-SQL
      WITH backwards_applies AS (
           SELECT invoice_payments.id,
              invoice_payments.payment_id,
              invoice_payments.invoice_id,
              invoice_payments.amount_cents,
              invoice_payments.created_at,
              invoice_payments.updated_at,
              invoice_payments.reversed_at,
              invoice_payments.date
             FROM ((invoice_payments
               JOIN invoices ON ((invoice_payments.invoice_id = invoices.id)))
               JOIN payments ON ((invoice_payments.payment_id = payments.id)))
            WHERE ((payments.date < invoices.post_date) AND (invoice_payments.reversed_at IS NULL))
          ), forwards_applies AS (
           SELECT invoice_payments.id,
              invoice_payments.payment_id,
              invoice_payments.invoice_id,
              invoice_payments.amount_cents,
              invoice_payments.created_at,
              invoice_payments.updated_at,
              invoice_payments.reversed_at,
              invoice_payments.date
             FROM ((invoice_payments
               JOIN invoices ON ((invoice_payments.invoice_id = invoices.id)))
               JOIN payments ON ((invoice_payments.payment_id = payments.id)))
            WHERE ((payments.date >= invoices.post_date) AND (invoice_payments.reversed_at IS NULL))
          ), up_events AS (
           SELECT payments.date,
              (payments.amount_cents - COALESCE(sum(forwards_applies.amount_cents), (0)::bigint)) AS amount_cents,
              payments.id AS payment_id,
              NULL::bigint AS invoice_payment_id
             FROM (payments
               LEFT JOIN forwards_applies ON ((forwards_applies.payment_id = payments.id)))
            GROUP BY payments.id
           HAVING (payments.amount_cents > COALESCE(sum(forwards_applies.amount_cents), (0)::bigint))
          ), down_events AS (
           SELECT invoices.post_date AS date,
              (backwards_applies.amount_cents * '-1'::integer) AS amount_cents,
              payments.id AS payment_id,
              backwards_applies.id AS invoice_payment_id
             FROM ((backwards_applies
               JOIN payments ON ((backwards_applies.payment_id = payments.id)))
               JOIN invoices ON ((backwards_applies.invoice_id = invoices.id)))
          )
   SELECT row_number() OVER (ORDER BY date) AS id,
      date,
      amount_cents,
      payment_id,
      invoice_payment_id
     FROM ( SELECT up_events.date,
              up_events.amount_cents,
              up_events.payment_id,
              up_events.invoice_payment_id
             FROM up_events
          UNION ALL
           SELECT down_events.date,
              down_events.amount_cents,
              down_events.payment_id,
              down_events.invoice_payment_id
             FROM down_events) results;
  SQL
  create_view "plutus_cash_accounts", materialized: true, sql_definition: <<-SQL
      SELECT id,
      name,
      type,
      contra,
      created_at,
      updated_at,
      tenant_id,
      tenant_type,
      category,
      gl_code,
      parent_id,
      description,
      header,
      owner_suppressed,
      passthrough
     FROM plutus_accounts
    WHERE ((NOT (id IN ( SELECT charts_of_accounts.accounts_receivable_id
             FROM charts_of_accounts
            WHERE (charts_of_accounts.accounts_receivable_id IS NOT NULL)))) AND (NOT (id IN ( SELECT charts_of_accounts.accounts_payable_id
             FROM charts_of_accounts
            WHERE (charts_of_accounts.accounts_payable_id IS NOT NULL)))));
  SQL
  create_view "lease_membership_aging_delinquencies", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), effective_invoice_total_payments AS (
           SELECT invoices.id AS invoice_id,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_payments.reversed_at IS NULL) OR (invoice_payments.reversed_at > effective_date_1.date)) THEN invoice_payments.amount_cents
                      ELSE 0
                  END), (0)::bigint) AS payment
             FROM ((invoices
               LEFT JOIN effective_date effective_date_1 ON (true))
               LEFT JOIN invoice_payments ON (((invoice_payments.invoice_id = invoices.id) AND (invoice_payments.date <= effective_date_1.date))))
            GROUP BY invoices.id
          ), invoice_balances AS (
           SELECT invoices.due_date,
                  CASE
                      WHEN ((invoices.waived_at IS NULL) OR (invoices.waived_at > effective_date_1.date)) THEN (invoices.amount_cents - effective_invoice_total_payments.payment)
                      ELSE (0)::bigint
                  END AS balance_cents,
              plutus_entries.lease_membership_id
             FROM (((invoices
               JOIN effective_invoice_total_payments ON ((effective_invoice_total_payments.invoice_id = invoices.id)))
               JOIN plutus_entries ON (((plutus_entries.commercial_document_id = invoices.id) AND ((plutus_entries.commercial_document_type)::text = 'Invoice'::text))))
               LEFT JOIN effective_date effective_date_1 ON (true))
            WHERE (invoices.post_date <= effective_date_1.date)
          )
   SELECT lease_memberships.id AS lease_membership_id,
      COALESCE(sum(invoice_balances.balance_cents), (0)::numeric) AS balance_cents,
      COALESCE(sum(
          CASE
              WHEN (invoice_balances.due_date < effective_date.date) THEN invoice_balances.balance_cents
              ELSE NULL::bigint
          END), (0)::numeric) AS overdue_cents,
      COALESCE(sum(
          CASE
              WHEN ((invoice_balances.due_date >= (effective_date.date - 'P30D'::interval)) AND (invoice_balances.due_date <= (effective_date.date - 'P1D'::interval))) THEN invoice_balances.balance_cents
              ELSE NULL::bigint
          END), (0)::numeric) AS thirty_days_cents,
      COALESCE(sum(
          CASE
              WHEN ((invoice_balances.due_date >= (effective_date.date - 'P60D'::interval)) AND (invoice_balances.due_date <= (effective_date.date - 'P31D'::interval))) THEN invoice_balances.balance_cents
              ELSE NULL::bigint
          END), (0)::numeric) AS sixty_days_cents,
      COALESCE(sum(
          CASE
              WHEN ((invoice_balances.due_date >= (effective_date.date - 'P90D'::interval)) AND (invoice_balances.due_date <= (effective_date.date - 'P61D'::interval))) THEN invoice_balances.balance_cents
              ELSE NULL::bigint
          END), (0)::numeric) AS ninety_days_cents,
      COALESCE(sum(
          CASE
              WHEN (invoice_balances.due_date <= (effective_date.date - 'P91D'::interval)) THEN invoice_balances.balance_cents
              ELSE NULL::bigint
          END), (0)::numeric) AS ninety_one_plus_days_cents
     FROM ((lease_memberships
       LEFT JOIN effective_date ON (true))
       LEFT JOIN invoice_balances ON ((invoice_balances.lease_membership_id = lease_memberships.id)))
    GROUP BY lease_memberships.id;
  SQL
  create_view "lease_chain_aging_delinquencies", sql_definition: <<-SQL
      SELECT lease_chains.id AS lease_chain_id,
      sum(lease_membership_aging_delinquencies.balance_cents) AS balance_cents,
      sum(lease_membership_aging_delinquencies.overdue_cents) AS overdue_cents,
      sum(lease_membership_aging_delinquencies.thirty_days_cents) AS thirty_days_cents,
      sum(lease_membership_aging_delinquencies.sixty_days_cents) AS sixty_days_cents,
      sum(lease_membership_aging_delinquencies.ninety_days_cents) AS ninety_days_cents,
      sum(lease_membership_aging_delinquencies.ninety_one_plus_days_cents) AS ninety_one_plus_days_cents
     FROM (lease_chains
       JOIN lease_membership_aging_delinquencies ON ((lease_membership_aging_delinquencies.lease_membership_id = ANY (lease_chains.lease_membership_ids))))
    GROUP BY lease_chains.id;
  SQL
  create_view "plutus_cash_entries", materialized: true, sql_definition: <<-SQL
      WITH unapplied_payment_entries AS (
           SELECT ((plutus_entries.journal_id || '_u_'::text) || unapplied_payment_activities.id) AS id,
              payments.description,
              payments.date,
              payments.id AS commercial_document_id,
              'Payment'::character varying AS commercial_document_type,
              plutus_entries.created_at,
              plutus_entries.updated_at,
              plutus_entries.property_id,
              plutus_entries.lease_membership_id,
              plutus_entries.journal_id,
              plutus_entries.unit_id,
              plutus_entries.tenant_id,
              plutus_entries.contact_name,
              plutus_entries.retained_earnings,
              (plutus_entries.reference_number)::text AS reference_number,
              (plutus_entries.reference_text)::text AS reference_text,
              2 AS basis,
              13 AS kind,
              0 AS invoice_payment_id
             FROM ((unapplied_payment_activities
               JOIN plutus_entries ON (((plutus_entries.commercial_document_id = unapplied_payment_activities.payment_id) AND ((plutus_entries.commercial_document_type)::text = 'Payment'::text) AND (plutus_entries.basis <> 3))))
               JOIN payments ON (((payments.id = unapplied_payment_activities.payment_id) AND (payments.reversed_at IS NULL))))
            WHERE (unapplied_payment_activities.amount_cents > 0)
          ), payment_entries AS (
           SELECT ((plutus_entries.journal_id || '_'::text) || invoice_payments.id) AS id,
              invoices.description,
                  CASE
                      WHEN (payments.date < invoices.post_date) THEN invoices.post_date
                      ELSE payments.date
                  END AS date,
              plutus_entries.commercial_document_id,
              plutus_entries.commercial_document_type,
              plutus_entries.created_at,
              plutus_entries.updated_at,
              plutus_entries.property_id,
              plutus_entries.lease_membership_id,
              plutus_entries.journal_id,
              plutus_entries.unit_id,
              plutus_entries.tenant_id,
              plutus_entries.contact_name,
              plutus_entries.retained_earnings,
              (plutus_entries.reference_number)::text AS reference_number,
              (plutus_entries.reference_text)::text AS reference_text,
              2 AS basis,
              11 AS kind,
              invoice_payments.id AS invoice_payment_id
             FROM (((plutus_entries
               JOIN payments ON (((plutus_entries.commercial_document_id = payments.id) AND ((plutus_entries.commercial_document_type)::text = 'Payment'::text) AND (payments.reversed_at IS NULL))))
               JOIN invoice_payments ON (((invoice_payments.payment_id = payments.id) AND (invoice_payments.reversed_at IS NULL))))
               JOIN invoices ON ((invoice_payments.invoice_id = invoices.id)))
            WHERE (plutus_entries.basis <> 3)
          ), direct_entries AS (
           SELECT (plutus_entries.id)::text AS id,
              plutus_entries.description,
              plutus_entries.date,
              plutus_entries.commercial_document_id,
              plutus_entries.commercial_document_type,
              plutus_entries.created_at,
              plutus_entries.updated_at,
              plutus_entries.property_id,
              plutus_entries.lease_membership_id,
              plutus_entries.journal_id,
              plutus_entries.unit_id,
              plutus_entries.tenant_id,
              plutus_entries.contact_name,
              plutus_entries.retained_earnings,
              (plutus_entries.reference_number)::text AS reference_number,
              (plutus_entries.reference_text)::text AS reference_text,
              plutus_entries.basis,
              0 AS kind,
              0 AS invoice_payment_id
             FROM plutus_entries
            WHERE ((plutus_entries.commercial_document_id IS NULL) AND (plutus_entries.basis <> 1) AND (plutus_entries.basis <> 3))
          )
   SELECT direct_entries.id,
      direct_entries.description,
      direct_entries.date,
      direct_entries.commercial_document_id,
      direct_entries.commercial_document_type,
      direct_entries.created_at,
      direct_entries.updated_at,
      direct_entries.property_id,
      direct_entries.lease_membership_id,
      direct_entries.journal_id,
      direct_entries.unit_id,
      direct_entries.tenant_id,
      direct_entries.contact_name,
      direct_entries.retained_earnings,
      direct_entries.reference_number,
      direct_entries.reference_text,
      direct_entries.basis,
      direct_entries.kind,
      direct_entries.invoice_payment_id
     FROM direct_entries
  UNION ALL
   SELECT unapplied_payment_entries.id,
      unapplied_payment_entries.description,
      unapplied_payment_entries.date,
      unapplied_payment_entries.commercial_document_id,
      unapplied_payment_entries.commercial_document_type,
      unapplied_payment_entries.created_at,
      unapplied_payment_entries.updated_at,
      unapplied_payment_entries.property_id,
      unapplied_payment_entries.lease_membership_id,
      unapplied_payment_entries.journal_id,
      unapplied_payment_entries.unit_id,
      unapplied_payment_entries.tenant_id,
      unapplied_payment_entries.contact_name,
      unapplied_payment_entries.retained_earnings,
      unapplied_payment_entries.reference_number,
      unapplied_payment_entries.reference_text,
      unapplied_payment_entries.basis,
      unapplied_payment_entries.kind,
      unapplied_payment_entries.invoice_payment_id
     FROM unapplied_payment_entries
  UNION ALL
   SELECT payment_entries.id,
      payment_entries.description,
      payment_entries.date,
      payment_entries.commercial_document_id,
      payment_entries.commercial_document_type,
      payment_entries.created_at,
      payment_entries.updated_at,
      payment_entries.property_id,
      payment_entries.lease_membership_id,
      payment_entries.journal_id,
      payment_entries.unit_id,
      payment_entries.tenant_id,
      payment_entries.contact_name,
      payment_entries.retained_earnings,
      payment_entries.reference_number,
      payment_entries.reference_text,
      payment_entries.basis,
      payment_entries.kind,
      payment_entries.invoice_payment_id
     FROM payment_entries;
  SQL
  add_index "plutus_cash_entries", ["commercial_document_id", "commercial_document_type"], name: "index_plutus_cash_entries_on_commercial_document"
  add_index "plutus_cash_entries", ["date"], name: "index_plutus_cash_entries_on_date"
  add_index "plutus_cash_entries", ["id"], name: "index_plutus_cash_entries_on_id"

  create_view "plutus_cash_amounts", materialized: true, sql_definition: <<-SQL
      WITH direct_amounts AS (
           SELECT plutus_amounts.id,
              plutus_amounts.type,
              plutus_amounts.account_id,
              (plutus_amounts.entry_id)::text AS entry_id,
              plutus_amounts.amount,
              plutus_amounts.reconciliation_id,
              plutus_amounts.deposit_batch_id,
              plutus_amounts.note
             FROM (plutus_amounts
               JOIN plutus_entries ON (((plutus_entries.id = plutus_amounts.entry_id) AND (plutus_entries.basis <> 1) AND (plutus_entries.basis <> 3) AND (plutus_entries.commercial_document_id IS NULL))))
          ), payment_prorations AS (
           SELECT payments.id AS payment_id,
              ((invoice_payments.amount_cents)::real / (COALESCE(NULLIF(payments.amount_cents, 0), 1))::real) AS prorating_factor,
              invoice_payments.id AS invoice_payment_id,
              (payments.date < invoices.post_date) AS from_unapplied
             FROM ((payments
               JOIN invoice_payments ON (((invoice_payments.payment_id = payments.id) AND (invoice_payments.reversed_at IS NULL))))
               JOIN invoices ON ((invoices.id = invoice_payments.invoice_id)))
          ), indirect_payment_amounts AS (
           SELECT plutus_amounts.id,
              plutus_amounts.type,
                  CASE
                      WHEN payment_prorations.from_unapplied THEN
                      CASE plutus_amounts.type
                          WHEN 'Plutus::DebitAmount'::text THEN charts_of_accounts.prepaid_revenue_account_id
                          WHEN 'Plutus::CreditAmount'::text THEN charts_of_accounts.prepaid_expense_account_id
                          ELSE NULL::bigint
                      END
                      ELSE (plutus_amounts.account_id)::bigint
                  END AS account_id,
              ((plutus_entries.journal_id || '_'::text) || payment_prorations.invoice_payment_id) AS entry_id,
              round(((plutus_amounts.amount)::double precision * payment_prorations.prorating_factor)) AS amount,
              plutus_amounts.reconciliation_id,
              plutus_amounts.deposit_batch_id,
              plutus_amounts.note
             FROM (((((plutus_amounts
               JOIN plutus_cash_accounts plutus_cash_accounts_1 ON ((plutus_cash_accounts_1.id = plutus_amounts.account_id)))
               JOIN plutus_entries ON (((plutus_entries.id = plutus_amounts.entry_id) AND (plutus_entries.basis <> 3))))
               JOIN payments ON (((payments.id = plutus_entries.commercial_document_id) AND ((plutus_entries.commercial_document_type)::text = 'Payment'::text))))
               JOIN payment_prorations ON ((payment_prorations.payment_id = payments.id)))
               JOIN charts_of_accounts ON ((charts_of_accounts.id = plutus_cash_accounts_1.tenant_id)))
          ), invoice_prorations AS (
           SELECT invoices.id AS invoice_id,
              ((invoice_payments.amount_cents)::real / (COALESCE(NULLIF(invoices.amount_cents, 0), 1))::real) AS prorating_factor,
              plutus_entries.id AS payment_entry_id,
              invoice_payments.id AS invoice_payment_id
             FROM (((invoices
               JOIN invoice_payments ON (((invoice_payments.invoice_id = invoices.id) AND (invoice_payments.reversed_at IS NULL))))
               JOIN payments ON ((invoice_payments.payment_id = payments.id)))
               JOIN plutus_entries ON (((plutus_entries.commercial_document_id = payments.id) AND ((plutus_entries.commercial_document_type)::text = 'Payment'::text))))
          ), indirect_invoice_amounts AS (
           SELECT plutus_amounts.id,
              plutus_amounts.type,
              plutus_amounts.account_id,
              ((plutus_entries.journal_id || '_'::text) || invoice_prorations.invoice_payment_id) AS entry_id,
              round(((plutus_amounts.amount)::double precision * invoice_prorations.prorating_factor)) AS amount,
              plutus_amounts.reconciliation_id,
              plutus_amounts.deposit_batch_id,
              plutus_amounts.note
             FROM (((plutus_amounts
               JOIN plutus_entries ON (((plutus_entries.id = plutus_amounts.entry_id) AND (plutus_entries.basis <> 3))))
               JOIN invoices ON (((invoices.id = plutus_entries.commercial_document_id) AND ((plutus_entries.commercial_document_type)::text = 'Invoice'::text))))
               JOIN invoice_prorations ON ((invoice_prorations.invoice_id = invoices.id)))
          ), unapplied_payment_amounts AS (
           SELECT plutus_amounts.id,
              plutus_amounts.type,
                  CASE
                      WHEN (plutus_cash_accounts_1.id IS NULL) THEN
                      CASE
                          WHEN ((plutus_amounts.type)::text = 'Plutus::CreditAmount'::text) THEN charts_of_accounts.prepaid_revenue_account_id
                          WHEN ((plutus_amounts.type)::text = 'Plutus::DebitAmount'::text) THEN charts_of_accounts.prepaid_expense_account_id
                          ELSE NULL::bigint
                      END
                      ELSE (plutus_amounts.account_id)::bigint
                  END AS account_id,
              ((plutus_entries.journal_id || '_u_'::text) || unapplied_payment_activities.id) AS entry_id,
                  CASE
                      WHEN (plutus_amounts.account_id = charts_of_accounts.merchant_processing_revenue_account_id) THEN (payments.convenience_fee_cents)::bigint
                      WHEN (plutus_amounts.account_id IN ( SELECT bank_accounts.ledger_account_id
                         FROM bank_accounts)) THEN (unapplied_payment_activities.amount_cents + payments.convenience_fee_cents)
                      ELSE unapplied_payment_activities.amount_cents
                  END AS amount,
              plutus_amounts.reconciliation_id,
              plutus_amounts.deposit_batch_id,
              plutus_amounts.note
             FROM ((((((unapplied_payment_activities
               JOIN payments ON ((unapplied_payment_activities.payment_id = payments.id)))
               JOIN plutus_entries ON ((((plutus_entries.commercial_document_type)::text = 'Payment'::text) AND (plutus_entries.commercial_document_id = payments.id) AND (plutus_entries.basis <> 3))))
               JOIN plutus_amounts ON ((plutus_amounts.entry_id = plutus_entries.id)))
               LEFT JOIN plutus_cash_accounts plutus_cash_accounts_1 ON ((plutus_cash_accounts_1.id = plutus_amounts.account_id)))
               JOIN plutus_accounts ON ((plutus_accounts.id = plutus_amounts.account_id)))
               JOIN charts_of_accounts ON ((charts_of_accounts.id = plutus_accounts.tenant_id)))
            WHERE (unapplied_payment_activities.amount_cents > 0)
          ), all_amounts AS (
           SELECT direct_amounts.id,
              direct_amounts.type,
              direct_amounts.account_id,
              direct_amounts.entry_id,
              direct_amounts.amount,
              direct_amounts.reconciliation_id,
              direct_amounts.deposit_batch_id,
              direct_amounts.note
             FROM direct_amounts
          UNION ALL
           SELECT indirect_payment_amounts.id,
              indirect_payment_amounts.type,
              indirect_payment_amounts.account_id,
              indirect_payment_amounts.entry_id,
              indirect_payment_amounts.amount,
              indirect_payment_amounts.reconciliation_id,
              indirect_payment_amounts.deposit_batch_id,
              indirect_payment_amounts.note
             FROM indirect_payment_amounts
          UNION ALL
           SELECT indirect_invoice_amounts.id,
              indirect_invoice_amounts.type,
              indirect_invoice_amounts.account_id,
              indirect_invoice_amounts.entry_id,
              indirect_invoice_amounts.amount,
              indirect_invoice_amounts.reconciliation_id,
              indirect_invoice_amounts.deposit_batch_id,
              indirect_invoice_amounts.note
             FROM indirect_invoice_amounts
          UNION ALL
           SELECT unapplied_payment_amounts.id,
              unapplied_payment_amounts.type,
              unapplied_payment_amounts.account_id,
              unapplied_payment_amounts.entry_id,
              unapplied_payment_amounts.amount,
              unapplied_payment_amounts.reconciliation_id,
              unapplied_payment_amounts.deposit_batch_id,
              unapplied_payment_amounts.note
             FROM unapplied_payment_amounts
          )
   SELECT DISTINCT ON (all_amounts.id, all_amounts.entry_id) all_amounts.id,
      all_amounts.type,
      all_amounts.account_id,
      all_amounts.entry_id,
      all_amounts.amount,
      all_amounts.reconciliation_id,
      all_amounts.deposit_batch_id,
      all_amounts.note
     FROM (all_amounts
       JOIN plutus_cash_accounts ON ((all_amounts.account_id = plutus_cash_accounts.id)));
  SQL
  add_index "plutus_cash_amounts", ["account_id", "entry_id"], name: "index_plutus_cash_amounts_on_account_id_and_entry_id"
  add_index "plutus_cash_amounts", ["account_id"], name: "index_plutus_cash_amounts_on_account_id"
  add_index "plutus_cash_amounts", ["entry_id", "account_id"], name: "index_plutus_cash_amounts_on_entry_id_and_account_id"
  add_index "plutus_cash_amounts", ["entry_id"], name: "index_plutus_cash_amounts_on_entry_id"
  add_index "plutus_cash_amounts", ["type"], name: "index_plutus_cash_amounts_on_type"

  create_view "lease_chain_statuses", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), active_evictions AS (
           SELECT collections_evictions.lease_id
             FROM (collections_evictions
               LEFT JOIN effective_date effective_date_1 ON (true))
            WHERE (((collections_evictions.created_at)::date <= effective_date_1.date) AND ((collections_evictions.closed_at IS NULL) OR (collections_evictions.closed_at >= effective_date_1.date)))
          ), active_notices_of_non_renewal AS (
           SELECT lease_notice_of_non_renewals.lease_id
             FROM (lease_notice_of_non_renewals
               LEFT JOIN effective_date effective_date_1 ON (true))
            WHERE (lease_notice_of_non_renewals.date <= effective_date_1.date)
          )
   SELECT lease_chains.id,
      lease_chains.id AS lease_chain_id,
          CASE
              WHEN (lease_chains.start_date > effective_date.date) THEN 2
              WHEN (lease_chains.end_date < effective_date.date) THEN 1
              WHEN (active_evictions.lease_id IS NOT NULL) THEN 4
              WHEN (active_notices_of_non_renewal.lease_id IS NOT NULL) THEN 3
              ELSE 0
          END AS status
     FROM (((lease_chains
       LEFT JOIN effective_date ON (true))
       LEFT JOIN active_evictions ON ((active_evictions.lease_id = ANY (lease_chains.lease_ids))))
       LEFT JOIN active_notices_of_non_renewal ON ((active_notices_of_non_renewal.lease_id = ANY (lease_chains.lease_ids))));
  SQL
  create_view "maintenance_work_order_accountings", sql_definition: <<-SQL
      SELECT maintenance_tickets.id,
      maintenance_tickets.id AS work_order_id,
      COALESCE(sum(invoices.amount_cents), (0)::bigint) AS total_invoice_amount_cents
     FROM ((maintenance_tickets
       LEFT JOIN linkages ON ((linkages.maintenance_ticket_id = maintenance_tickets.id)))
       LEFT JOIN invoices ON ((invoices.id = linkages.invoice_id)))
    GROUP BY maintenance_tickets.id;
  SQL
  create_view "tenant_statuses", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), active_lease_memberships AS (
           SELECT lease_memberships.tenant_id
             FROM ((lease_memberships
               JOIN effective_date ON (true))
               JOIN leases ON ((leases.id = lease_memberships.lease_id)))
            WHERE (((leases.archived_at IS NULL) OR (leases.archived_at > effective_date.date)) AND (leases.start_date <= effective_date.date) AND (leases.end_date >= effective_date.date))
          ), upcoming_lease_memberships AS (
           SELECT lease_memberships.tenant_id
             FROM ((lease_memberships
               JOIN effective_date ON (true))
               JOIN leases ON ((leases.id = lease_memberships.lease_id)))
            WHERE (((leases.archived_at IS NULL) OR (leases.archived_at > effective_date.date)) AND (leases.start_date > effective_date.date))
          ), previous_lease_memberships AS (
           SELECT lease_memberships.tenant_id
             FROM ((lease_memberships
               JOIN effective_date ON (true))
               JOIN leases ON ((leases.id = lease_memberships.lease_id)))
            WHERE (((leases.archived_at IS NULL) OR (leases.archived_at > effective_date.date)) AND (leases.end_date < effective_date.date))
          ), submitted_application_memberships AS (
           SELECT lease_application_memberships.tenant_id
             FROM ((lease_application_memberships
               JOIN effective_date ON (true))
               JOIN lease_applications ON ((lease_application_memberships.lease_application_id = lease_applications.id)))
            WHERE ((lease_applications.submitted_at IS NOT NULL) AND ((lease_applications.submitted_at)::date <= effective_date.date))
          )
   SELECT id,
      id AS tenant_id,
          CASE
              WHEN (EXISTS ( SELECT active_lease_memberships.tenant_id
                 FROM active_lease_memberships
                WHERE (active_lease_memberships.tenant_id = tenants.id))) THEN 1
              WHEN (EXISTS ( SELECT upcoming_lease_memberships.tenant_id
                 FROM upcoming_lease_memberships
                WHERE (upcoming_lease_memberships.tenant_id = tenants.id))) THEN 2
              WHEN (EXISTS ( SELECT previous_lease_memberships.tenant_id
                 FROM previous_lease_memberships
                WHERE (previous_lease_memberships.tenant_id = tenants.id))) THEN 3
              WHEN (EXISTS ( SELECT submitted_application_memberships.tenant_id
                 FROM submitted_application_memberships
                WHERE (submitted_application_memberships.tenant_id = tenants.id))) THEN 4
              ELSE 0
          END AS status
     FROM tenants;
  SQL
  create_view "tenant_prospect_data", sql_definition: <<-SQL
      WITH text_messages AS (
           SELECT telephony_text_messages.recipient_id AS tenant_id,
              (telephony_text_messages.created_at)::date AS contacted_at
             FROM telephony_text_messages
            WHERE ((telephony_text_messages.recipient_type)::text = 'Tenant'::text)
          ), email_receipt_dates AS (
           SELECT email_receipts.created_at AS contacted_at,
              unnest(((email_receipts."to" || email_receipts.cc) || email_receipts.bcc)) AS email
             FROM email_receipts
          ), tenant_email_receipts AS (
           SELECT tenants_1.id AS tenant_id,
              email_receipt_dates.contacted_at
             FROM (email_receipt_dates
               JOIN tenants tenants_1 ON (((tenants_1.email)::text = (email_receipt_dates.email)::text)))
          ), contact_times AS (
           SELECT last_contacts.tenant_id,
              min(last_contacts.contacted_at) AS first_contacted_at,
              max(last_contacts.contacted_at) AS last_contacted_at
             FROM ( SELECT text_messages.tenant_id,
                      text_messages.contacted_at
                     FROM text_messages
                  UNION ALL
                   SELECT tenant_email_receipts.tenant_id,
                      tenant_email_receipts.contacted_at
                     FROM tenant_email_receipts) last_contacts
            GROUP BY last_contacts.tenant_id
          ), most_recent_guest_cards AS (
           SELECT DISTINCT ON (guest_cards.tenant_id) guest_cards.tenant_id,
              (guest_cards.created_at)::date AS guest_card_date,
              guest_cards.updated_at,
              guest_cards.property_id,
              sources.source,
              'Submitted Guest Card'::text AS status
             FROM (guest_cards
               LEFT JOIN sources ON ((sources.id = guest_cards.external_source_id)))
            ORDER BY guest_cards.tenant_id, guest_cards.created_at DESC
          ), most_recent_tours AS (
           SELECT DISTINCT ON (tours.lead_id) tours.lead_id AS tenant_id,
              (tours."time")::date AS tour_date,
              tours.property_id,
              tours.updated_at,
              sources.source,
              'Toured'::text AS status
             FROM (tours
               LEFT JOIN sources ON ((sources.id = tours.source_id)))
            ORDER BY tours.lead_id, tours."time" DESC
          ), most_recent_applications AS (
           SELECT DISTINCT ON (lease_application_memberships.tenant_id) lease_application_memberships.tenant_id,
              (lease_application_memberships.created_at)::date AS application_date,
              lease_applications.property_id,
              lease_applications.updated_at,
                  CASE
                      WHEN (lease_applications.rejected_at IS NOT NULL) THEN 'Application Rejected'::text
                      WHEN (lease_applications.approved_at IS NOT NULL) THEN 'Application Approved'::text
                      ELSE 'Application Submitted'::text
                  END AS status,
              lease_application_memberships."primary"
             FROM (lease_application_memberships
               JOIN lease_applications ON ((lease_applications.id = lease_application_memberships.lease_application_id)))
            ORDER BY lease_application_memberships.tenant_id, lease_application_memberships.created_at DESC
          ), most_recent_leases AS (
           SELECT DISTINCT ON (lease_memberships.tenant_id) lease_memberships.tenant_id,
              (lease_memberships.created_at)::date AS lease_date,
              lease_memberships.updated_at,
              units.property_id,
                  CASE
                      WHEN (renewed.id IS NOT NULL) THEN 'Renewed'::text
                      ELSE 'Leased'::text
                  END AS status,
                  CASE
                      WHEN (lease_memberships.role = 0) THEN true
                      ELSE false
                  END AS "primary"
             FROM (((lease_memberships
               JOIN leases ON (((leases.id = lease_memberships.lease_id) AND (leases.archived_at IS NULL))))
               LEFT JOIN leases renewed ON ((renewed.renewal_id = leases.id)))
               JOIN units ON ((units.id = leases.unit_id)))
            ORDER BY lease_memberships.tenant_id, lease_memberships.created_at DESC
          ), guest_card_counts AS (
           SELECT guest_cards.tenant_id,
              count(*) AS guest_card_count
             FROM guest_cards
            GROUP BY guest_cards.tenant_id
          ), tour_counts AS (
           SELECT tours.lead_id AS tenant_id,
              count(*) AS tour_count
             FROM tours
            GROUP BY tours.lead_id
          ), application_counts AS (
           SELECT lease_application_memberships.tenant_id,
              count(*) AS application_count
             FROM lease_application_memberships
            GROUP BY lease_application_memberships.tenant_id
          ), lease_counts AS (
           SELECT lease_memberships.tenant_id,
              count(*) AS lease_count
             FROM lease_memberships
            GROUP BY lease_memberships.tenant_id
          )
   SELECT tenants.id,
      tenants.id AS tenant_id,
      COALESCE((most_recent_leases.property_id)::bigint, (most_recent_applications.property_id)::bigint, most_recent_tours.property_id, (most_recent_guest_cards.property_id)::bigint) AS property_id,
      COALESCE(most_recent_leases.status, most_recent_applications.status, most_recent_tours.status, most_recent_guest_cards.status) AS status,
      tenants.first_name,
      tenants.last_name,
      tenants.email,
      tenants.phone,
      COALESCE(most_recent_guest_cards.source, most_recent_tours.source) AS source,
      COALESCE(guest_card_counts.guest_card_count, (0)::bigint) AS guest_card_count,
      most_recent_guest_cards.guest_card_date AS last_guest_card,
      COALESCE(tour_counts.tour_count, (0)::bigint) AS tour_count,
      most_recent_tours.tour_date AS last_tour,
      COALESCE(application_counts.application_count, (0)::bigint) AS application_count,
      most_recent_applications.application_date AS last_application,
      COALESCE(lease_counts.lease_count, (0)::bigint) AS lease_count,
      most_recent_leases.lease_date AS last_lease,
      contact_times.first_contacted_at,
      contact_times.last_contacted_at,
      tenants.created_at,
      GREATEST(((tenants.updated_at)::date)::timestamp without time zone, most_recent_guest_cards.updated_at, most_recent_tours.updated_at, most_recent_applications.updated_at, most_recent_leases.updated_at, contact_times.last_contacted_at) AS updated_at
     FROM (((((((((((tenants
       LEFT JOIN guest_card_counts ON ((guest_card_counts.tenant_id = tenants.id)))
       LEFT JOIN most_recent_guest_cards ON ((most_recent_guest_cards.tenant_id = tenants.id)))
       LEFT JOIN tour_counts ON ((tour_counts.tenant_id = tenants.id)))
       LEFT JOIN most_recent_tours ON ((most_recent_tours.tenant_id = tenants.id)))
       LEFT JOIN application_counts ON ((application_counts.tenant_id = tenants.id)))
       LEFT JOIN most_recent_applications ON ((most_recent_applications.tenant_id = tenants.id)))
       LEFT JOIN most_recent_applications most_recent_primary_applications ON (((most_recent_primary_applications.tenant_id = tenants.id) AND (most_recent_primary_applications."primary" = true))))
       LEFT JOIN lease_counts ON ((lease_counts.tenant_id = tenants.id)))
       LEFT JOIN most_recent_leases ON ((most_recent_leases.tenant_id = tenants.id)))
       LEFT JOIN most_recent_leases most_recent_primary_leases ON (((most_recent_primary_leases.tenant_id = tenants.id) AND (most_recent_primary_leases."primary" = true))))
       LEFT JOIN contact_times ON ((contact_times.tenant_id = tenants.id)))
    WHERE (COALESCE(most_recent_guest_cards.guest_card_date, most_recent_tours.tour_date, most_recent_primary_applications.application_date) IS NOT NULL);
  SQL
  create_view "lease_renewal_statuses", sql_definition: <<-SQL
      WITH signatures_sent AS (
           SELECT leases_1.id AS lease_id,
                  CASE
                      WHEN (leases_1.id IN ( SELECT electronic_signatures.document_id
                         FROM electronic_signatures
                        WHERE ((electronic_signatures.document_type)::text = 'Lease'::text))) THEN 1
                      ELSE 0
                  END AS signatures_sent
             FROM leases leases_1
          )
   SELECT leases.id,
      leases.id AS lease_id,
          CASE
              WHEN (renewals.id IS NULL) THEN
              CASE
                  WHEN (non_renewals.id IS NOT NULL) THEN 7
                  ELSE
                  CASE leases.kind
                      WHEN 0 THEN 0
                      WHEN 2 THEN 1
                      WHEN 1 THEN 2
                      ELSE NULL::integer
                  END
              END
              ELSE
              CASE
                  WHEN (renewals.executed_at IS NOT NULL) THEN 10
                  ELSE
                  CASE
                      WHEN (renewal_signatures_sent.signatures_sent = 1) THEN 9
                      ELSE 8
                  END
              END
          END AS status
     FROM (((leases
       LEFT JOIN lease_notice_of_non_renewals non_renewals ON ((non_renewals.lease_id = leases.id)))
       LEFT JOIN leases renewals ON ((renewals.id = leases.renewal_id)))
       LEFT JOIN signatures_sent renewal_signatures_sent ON ((renewal_signatures_sent.lease_id = renewals.id)));
  SQL
  create_view "lease_chain_aging_delinquencies_materialized", materialized: true, sql_definition: <<-SQL
      SELECT lease_chain_id,
      balance_cents,
      overdue_cents,
      thirty_days_cents,
      sixty_days_cents,
      ninety_days_cents,
      ninety_one_plus_days_cents
     FROM lease_chain_aging_delinquencies;
  SQL
  create_view "lease_chain_active_evictions", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), active_evictions AS (
           SELECT max(evictions.id) AS eviction_id,
              lease_chains_1.id AS lease_chain_id
             FROM ((collections_evictions evictions
               JOIN effective_date ON (true))
               JOIN lease_chains lease_chains_1 ON ((evictions.lease_id = ANY (lease_chains_1.lease_ids))))
            WHERE (((evictions.created_at)::date <= effective_date.date) AND ((evictions.closed_at IS NULL) OR ((evictions.closed_at)::date > effective_date.date)))
            GROUP BY lease_chains_1.id
          ), most_recent_evictions AS (
           SELECT DISTINCT ON (lease_chains_1.id) lease_chains_1.id AS lease_chain_id,
              evictions.id AS eviction_id
             FROM ((collections_evictions evictions
               JOIN effective_date ON (true))
               JOIN lease_chains lease_chains_1 ON ((evictions.lease_id = ANY (lease_chains_1.lease_ids))))
            WHERE ((evictions.created_at)::date <= effective_date.date)
            ORDER BY lease_chains_1.id, evictions.created_at DESC
          )
   SELECT lease_chains.id,
      lease_chains.id AS lease_chain_id,
      active_evictions.eviction_id AS active_eviction_id,
      most_recent_evictions.eviction_id AS most_recent_eviction_id
     FROM ((lease_chains
       LEFT JOIN active_evictions ON ((lease_chains.id = active_evictions.lease_chain_id)))
       LEFT JOIN most_recent_evictions ON ((lease_chains.id = most_recent_evictions.lease_chain_id)));
  SQL
  create_view "reconciliation_items", sql_definition: <<-SQL
      WITH bare_amounts AS (
           SELECT plutus_amounts.id,
              plutus_entries.journal_id,
              plutus_amounts.reconciliation_id,
              plutus_accounts.id AS account_id,
              plutus_entries.id AS entry_id,
                  CASE plutus_entries.commercial_document_type
                      WHEN 'Payment'::text THEN plutus_entries.commercial_document_id
                      ELSE NULL::integer
                  END AS payment_id,
              NULL::integer AS transaction_batch_id,
              plutus_entries.date,
              plutus_entries.description,
              plutus_entries.property_id,
              plutus_entries.contact_name,
              plutus_entries.reference_number,
              plutus_entries.reference_text,
                  CASE plutus_amounts.type
                      WHEN 'Plutus::DebitAmount'::text THEN plutus_amounts.amount
                      ELSE (- plutus_amounts.amount)
                  END AS amount_cents
             FROM ((plutus_amounts
               JOIN plutus_entries ON ((plutus_amounts.entry_id = plutus_entries.id)))
               JOIN plutus_accounts ON ((plutus_accounts.id = plutus_amounts.account_id)))
            WHERE ((plutus_amounts.deposit_batch_id IS NULL) AND (plutus_accounts.id IN ( SELECT bank_accounts.ledger_account_id
                     FROM bank_accounts)) AND (plutus_entries.basis <> 2) AND (plutus_entries.basis <> 3))
          ), transaction_batches AS (
           SELECT deposit_batches.id,
              bank_accounts.owner_id AS journal_id,
              deposit_batches.reconciliation_id,
              bank_accounts.ledger_account_id AS account_id,
              NULL::integer AS entry_id,
              NULL::integer AS payment_id,
              deposit_batches.id AS transaction_batch_id,
              deposit_batches.expected_date AS date,
              deposit_batches.name AS description,
              NULL::integer AS property_id,
              NULL::text AS contact_name,
              NULL::text AS reference_number,
                  CASE deposit_batches.expected_count
                      WHEN 1 THEN '1 Item'::text
                      ELSE (deposit_batches.expected_count || ' Items'::text)
                  END AS reference_text,
              sum(
                  CASE plutus_amounts.type
                      WHEN 'Plutus::DebitAmount'::text THEN plutus_amounts.amount
                      ELSE (- plutus_amounts.amount)
                  END) AS amount_cents
             FROM ((deposit_batches
               JOIN bank_accounts ON ((bank_accounts.id = deposit_batches.bank_account_id)))
               JOIN plutus_amounts ON ((plutus_amounts.deposit_batch_id = deposit_batches.id)))
            WHERE (deposit_batches.posted_at IS NOT NULL)
            GROUP BY deposit_batches.id, bank_accounts.owner_id, bank_accounts.ledger_account_id
          )
   SELECT bare_amounts.id,
      bare_amounts.journal_id,
      bare_amounts.reconciliation_id,
      bare_amounts.account_id,
      bare_amounts.entry_id,
      bare_amounts.payment_id,
      bare_amounts.transaction_batch_id,
      bare_amounts.date,
      bare_amounts.description,
      bare_amounts.property_id,
      bare_amounts.contact_name,
      bare_amounts.reference_number,
      bare_amounts.reference_text,
      bare_amounts.amount_cents
     FROM bare_amounts
  UNION ALL
   SELECT transaction_batches.id,
      transaction_batches.journal_id,
      transaction_batches.reconciliation_id,
      transaction_batches.account_id,
      transaction_batches.entry_id,
      transaction_batches.payment_id,
      transaction_batches.transaction_batch_id,
      transaction_batches.date,
      transaction_batches.description,
      transaction_batches.property_id,
      transaction_batches.contact_name,
      transaction_batches.reference_number,
      transaction_batches.reference_text,
      transaction_batches.amount_cents
     FROM transaction_batches;
  SQL
  create_view "line_item_receivable_balances", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), effective_invoice_total_payments AS (
           SELECT invoices_1.id AS invoice_id,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_payments.reversed_at IS NULL) OR (invoice_payments.reversed_at > effective_date_1.date)) THEN invoice_payments.amount_cents
                      ELSE 0
                  END), (0)::bigint) AS payment
             FROM ((invoices invoices_1
               LEFT JOIN effective_date effective_date_1 ON (true))
               LEFT JOIN invoice_payments ON (((invoice_payments.invoice_id = invoices_1.id) AND (invoice_payments.date <= effective_date_1.date))))
            GROUP BY invoices_1.id
          ), account_priority AS (
           SELECT plutus_accounts_1.id AS account_id,
                  CASE
                      WHEN (plutus_accounts_1.id IN ( SELECT charge_presets.account_id
                         FROM charge_presets
                        WHERE (charge_presets.kind = 5))) THEN 1
                      ELSE
                      CASE
                          WHEN ((plutus_accounts_1.category)::text = 'Rent'::text) THEN 2
                          ELSE 3
                      END
                  END AS priority
             FROM plutus_accounts plutus_accounts_1
          )
   SELECT line_items.id,
      line_items.id AS line_item_id,
      invoices.id AS invoice_id,
      plutus_accounts.id AS account_id,
          CASE
              WHEN ((invoices.waived_at IS NULL) OR (invoices.waived_at > effective_date.date)) THEN ((line_items.unit_price_cents * line_items.quantity) -
              CASE
                  WHEN ((COALESCE(effective_invoice_total_payments.payment, (0)::bigint) - sum((line_items.unit_price_cents * line_items.quantity)) OVER prioritized_invoice_line_items) < 0) THEN GREATEST(((line_items.unit_price_cents * line_items.quantity) + (COALESCE(effective_invoice_total_payments.payment, (0)::bigint) - sum((line_items.unit_price_cents * line_items.quantity)) OVER prioritized_invoice_line_items)), (0)::bigint)
                  ELSE ((line_items.unit_price_cents * line_items.quantity))::bigint
              END)
              ELSE (0)::bigint
          END AS balance_cents
     FROM (((((line_items
       LEFT JOIN effective_date ON (true))
       JOIN invoices ON ((line_items.invoice_id = invoices.id)))
       JOIN plutus_accounts ON ((line_items.receivable_account_id = plutus_accounts.id)))
       JOIN account_priority ON ((account_priority.account_id = plutus_accounts.id)))
       LEFT JOIN effective_invoice_total_payments ON ((effective_invoice_total_payments.invoice_id = invoices.id)))
    WINDOW prioritized_invoice_line_items AS (PARTITION BY invoices.id ORDER BY account_priority.priority, plutus_accounts.gl_code, line_items.id ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
  SQL
  create_view "line_item_payable_balances", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), effective_invoice_total_payments AS (
           SELECT invoices_1.id AS invoice_id,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_payments.reversed_at IS NULL) OR (invoice_payments.reversed_at > effective_date_1.date)) THEN invoice_payments.amount_cents
                      ELSE 0
                  END), (0)::bigint) AS payment
             FROM ((invoices invoices_1
               LEFT JOIN effective_date effective_date_1 ON (true))
               LEFT JOIN invoice_payments ON (((invoice_payments.invoice_id = invoices_1.id) AND (invoice_payments.date <= effective_date_1.date))))
            GROUP BY invoices_1.id
          )
   SELECT line_items.id,
      line_items.id AS line_item_id,
      invoices.id AS invoice_id,
      plutus_accounts.id AS account_id,
          CASE
              WHEN ((invoices.waived_at IS NULL) OR (invoices.waived_at > effective_date.date)) THEN ((line_items.unit_price_cents * line_items.quantity) -
              CASE
                  WHEN ((COALESCE(effective_invoice_total_payments.payment, (0)::bigint) - sum((line_items.unit_price_cents * line_items.quantity)) OVER prioritized_invoice_line_items) < 0) THEN GREATEST(((line_items.unit_price_cents * line_items.quantity) + (COALESCE(effective_invoice_total_payments.payment, (0)::bigint) - sum((line_items.unit_price_cents * line_items.quantity)) OVER prioritized_invoice_line_items)), (0)::bigint)
                  ELSE ((line_items.unit_price_cents * line_items.quantity))::bigint
              END)
              ELSE (0)::bigint
          END AS balance_cents
     FROM ((((line_items
       LEFT JOIN effective_date ON (true))
       JOIN invoices ON ((line_items.invoice_id = invoices.id)))
       JOIN plutus_accounts ON ((line_items.payable_account_id = plutus_accounts.id)))
       LEFT JOIN effective_invoice_total_payments ON ((effective_invoice_total_payments.invoice_id = invoices.id)))
    WINDOW prioritized_invoice_line_items AS (PARTITION BY invoices.id ORDER BY plutus_accounts.gl_code, plutus_accounts.id, line_items.id ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
  SQL
  create_view "security_deposit_amounts", sql_definition: <<-SQL
      WITH deposit_account_ids AS (
           SELECT charts_of_accounts.security_deposit_account_id AS id
             FROM charts_of_accounts
          UNION ALL
           SELECT charts_of_accounts.management_held_security_deposit_account_id AS id
             FROM charts_of_accounts
          ), lease_chain_primary_tenant_ids AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              min(lease_memberships.tenant_id) AS primary_tenant_id
             FROM (lease_chains lease_chains_1
               JOIN lease_memberships ON ((lease_memberships.id = ANY (lease_chains_1.lease_membership_ids))))
            GROUP BY lease_chains_1.id
          ), accrual_amounts AS (
           SELECT (plutus_entries.id)::text AS id,
              'accrual'::text AS kind,
              plutus_amounts.type,
              plutus_amounts.amount AS amount_cents,
              COALESCE(invoices.buyer_lease_membership_id, (plutus_entries.lease_membership_id)::bigint) AS lease_membership_id,
              plutus_entries.date,
                  CASE
                      WHEN ((plutus_amounts.type)::text = 'Plutus::CreditAmount'::text) THEN plutus_amounts.amount
                      ELSE (0)::numeric
                  END AS charged_cents,
                  CASE
                      WHEN ((plutus_amounts.type)::text = 'Plutus::CreditAmount'::text) THEN plutus_amounts.amount
                      ELSE (- plutus_amounts.amount)
                  END AS liability_cents,
              0 AS paid_cents,
              0 AS held_cents
             FROM ((plutus_amounts
               JOIN plutus_entries ON (((plutus_amounts.entry_id = plutus_entries.id) AND (plutus_entries.basis <> 2) AND (plutus_entries.basis <> 3))))
               LEFT JOIN invoices ON ((((plutus_entries.commercial_document_type)::text = 'Invoice'::text) AND (plutus_entries.commercial_document_id = invoices.id))))
            WHERE (plutus_amounts.account_id IN ( SELECT deposit_account_ids.id
                     FROM deposit_account_ids))
          ), cash_amounts AS (
           SELECT plutus_cash_entries.id,
              'cash'::text AS kind,
              plutus_cash_amounts.type,
              plutus_cash_amounts.amount AS amount_cents,
              plutus_cash_entries.lease_membership_id,
              plutus_cash_entries.date,
              0 AS charged_cents,
              0 AS liability_cents,
                  CASE
                      WHEN ((plutus_cash_amounts.type)::text = 'Plutus::CreditAmount'::text) THEN plutus_cash_amounts.amount
                      ELSE (0)::double precision
                  END AS paid_cents,
                  CASE
                      WHEN ((plutus_cash_amounts.type)::text = 'Plutus::CreditAmount'::text) THEN plutus_cash_amounts.amount
                      ELSE (- plutus_cash_amounts.amount)
                  END AS held_cents
             FROM (plutus_cash_amounts
               JOIN plutus_cash_entries ON (((plutus_cash_amounts.entry_id = plutus_cash_entries.id) AND (plutus_cash_entries.basis <> 1))))
            WHERE (plutus_cash_amounts.account_id IN ( SELECT deposit_account_ids.id
                     FROM deposit_account_ids))
          ), all_amounts AS (
           SELECT accrual_amounts.id,
              accrual_amounts.kind,
              accrual_amounts.type,
              accrual_amounts.amount_cents,
              accrual_amounts.lease_membership_id,
              accrual_amounts.date,
              accrual_amounts.charged_cents,
              accrual_amounts.liability_cents,
              accrual_amounts.paid_cents,
              accrual_amounts.held_cents
             FROM accrual_amounts
          UNION ALL
           SELECT cash_amounts.id,
              cash_amounts.kind,
              cash_amounts.type,
              cash_amounts.amount_cents,
              cash_amounts.lease_membership_id,
              cash_amounts.date,
              cash_amounts.charged_cents,
              cash_amounts.liability_cents,
              cash_amounts.paid_cents,
              cash_amounts.held_cents
             FROM cash_amounts
          )
   SELECT all_amounts.id,
      lease_chains.id AS lease_chain_id,
      lease_chains.current_unit_id AS unit_id,
      units.property_id,
      lease_chain_primary_tenant_ids.primary_tenant_id AS tenant_id,
      all_amounts.date,
      all_amounts.charged_cents,
      all_amounts.liability_cents,
      all_amounts.paid_cents,
      all_amounts.held_cents
     FROM (((lease_chains
       JOIN units ON ((lease_chains.current_unit_id = units.id)))
       JOIN lease_chain_primary_tenant_ids ON ((lease_chains.id = lease_chain_primary_tenant_ids.lease_chain_id)))
       LEFT JOIN all_amounts ON ((all_amounts.lease_membership_id = ANY (lease_chains.lease_membership_ids))));
  SQL
  create_view "security_deposits", sql_definition: <<-SQL
      WITH deposit_accounts AS (
           SELECT charts_of_accounts.security_deposit_account_id AS id
             FROM charts_of_accounts
          UNION ALL
           SELECT charts_of_accounts.management_held_security_deposit_account_id AS id
             FROM charts_of_accounts
          ), deposit_ratios AS (
           SELECT invoices.id,
              (sum(
                  CASE
                      WHEN (line_items.receivable_account_id IN ( SELECT deposit_accounts.id
                         FROM deposit_accounts)) THEN ((line_items.unit_price_cents * line_items.quantity))::numeric
                      ELSE 0.0
                  END) / (invoices.amount_cents)::numeric) AS ratio
             FROM (invoices
               LEFT JOIN line_items ON ((line_items.invoice_id = invoices.id)))
            GROUP BY invoices.id
          ), deposit_activity AS (
           SELECT plutus_entries.id,
              COALESCE(invoices.buyer_lease_membership_id, (plutus_entries.lease_membership_id)::bigint) AS lease_membership_id,
              credit_accounts.type AS credit_type,
                  CASE plutus_entries.kind
                      WHEN 15 THEN 'Plutus::Asset'::character varying
                      ELSE debit_accounts.type
                  END AS debit_type,
              credit_amounts.amount AS credit_amount,
              debit_amounts.amount AS debit_amount,
              invoices.post_date AS date,
              ((invoice_total_payments.payment)::numeric * deposit_ratios.ratio) AS payment_amount
             FROM (((((((plutus_entries
               LEFT JOIN plutus_amounts credit_amounts ON (((credit_amounts.entry_id = plutus_entries.id) AND ((credit_amounts.type)::text = 'Plutus::CreditAmount'::text))))
               LEFT JOIN plutus_amounts debit_amounts ON (((debit_amounts.entry_id = plutus_entries.id) AND ((debit_amounts.type)::text = 'Plutus::DebitAmount'::text))))
               LEFT JOIN plutus_accounts credit_accounts ON ((credit_amounts.account_id = credit_accounts.id)))
               LEFT JOIN plutus_accounts debit_accounts ON ((debit_amounts.account_id = debit_accounts.id)))
               LEFT JOIN invoices ON (((invoices.id = plutus_entries.commercial_document_id) AND ((plutus_entries.commercial_document_type)::text = 'Invoice'::text) AND ((invoices.buyer_type)::text = 'Tenant'::text))))
               LEFT JOIN invoice_total_payments ON ((invoice_total_payments.invoice_id = invoices.id)))
               LEFT JOIN deposit_ratios ON ((deposit_ratios.id = invoices.id)))
            WHERE (((credit_accounts.id IN ( SELECT deposit_accounts.id
                     FROM deposit_accounts)) OR (debit_accounts.id IN ( SELECT deposit_accounts.id
                     FROM deposit_accounts))) AND (plutus_entries.basis <> 3))
          )
   SELECT lease_membership_id,
      min(date) AS date,
      sum(
          CASE
              WHEN ((debit_type)::text = 'Plutus::Asset'::text) THEN credit_amount
              ELSE (0)::numeric
          END) AS charged_cents,
      sum(
          CASE
              WHEN ((credit_type)::text = 'Plutus::Asset'::text) THEN debit_amount
              ELSE (0)::numeric
          END) AS deducted_cents,
      sum(
          CASE
              WHEN (((debit_type)::text = 'Plutus::Liability'::text) AND ((credit_type)::text = 'Plutus::Liability'::text)) THEN debit_amount
              ELSE (0)::numeric
          END) AS returned_cents,
      COALESCE(sum(payment_amount), (0)::numeric) AS paid_cents
     FROM deposit_activity
    WHERE (lease_membership_id IS NOT NULL)
    GROUP BY lease_membership_id;
  SQL
  create_view "taxes_annual_owner_amounts", materialized: true, sql_definition: <<-SQL
      SELECT ( SELECT companies.id
             FROM companies
            WHERE (companies.customer_managed = false)
           LIMIT 1) AS company_id,
      managed_companies.id AS owner_id,
      (EXTRACT(year FROM entries.date))::integer AS year,
      sum(
          CASE
              WHEN ((amounts.type)::text = 'Plutus::CreditAmount'::text) THEN amounts.amount
              ELSE (- amounts.amount)
          END) AS amount_cents
     FROM (((plutus_cash_amounts amounts
       JOIN plutus_cash_entries entries ON ((entries.id = amounts.entry_id)))
       JOIN plutus_cash_accounts accounts ON ((accounts.id = amounts.account_id)))
       JOIN companies managed_companies ON ((entries.journal_id = managed_companies.id)))
    WHERE (((accounts.type)::text = 'Plutus::Revenue'::text) AND (managed_companies.customer_managed = true))
    GROUP BY managed_companies.id, ((EXTRACT(year FROM entries.date))::integer);
  SQL
  create_view "taxes_annual_vendor_amounts", materialized: true, sql_definition: <<-SQL
      SELECT companies.id AS company_id,
      payments.payee_id AS vendor_id,
      (EXTRACT(year FROM payments.date))::integer AS year,
      sum(payments.amount_cents) AS amount_cents
     FROM (((payments
       LEFT JOIN properties ON ((((payments.payer_type)::text = 'Property'::text) AND (properties.id = payments.payer_id))))
       JOIN companies ON (((companies.id = properties.company_id) OR (((payments.payer_type)::text = 'Company'::text) AND (companies.id = payments.payer_id)))))
       JOIN vendors ON ((((payments.payee_type)::text = 'Vendor'::text) AND (vendors.id = payments.payee_id))))
    WHERE (((vendors.business_type <> ALL (ARRAY[1, 2])) OR (vendors.business_type IS NULL)) AND (vendors.kind <> ALL (ARRAY[1, 3, 4])) AND (companies.customer_managed = false) AND (payments.reversed_at IS NULL))
    GROUP BY companies.id, payments.payee_id, ((EXTRACT(year FROM payments.date))::integer);
  SQL
  create_view "taxes_candidate1099s", sql_definition: <<-SQL
      WITH owner_payees AS (
           SELECT DISTINCT concat(payees.id, 'Company', owner_amounts.year, payers.id) AS id,
              payees.id AS payee_id,
              payees.name AS payee_name,
              'Company'::text AS payee_type,
              payers.id AS payer_id,
              payers.name AS payer_name,
              owner_amounts.amount_cents AS payments_total_cents,
              owner_amounts.year
             FROM ((taxes_annual_owner_amounts owner_amounts
               JOIN companies payers ON ((owner_amounts.company_id = payers.id)))
               JOIN companies payees ON ((owner_amounts.owner_id = payees.id)))
          ), vendor_payees AS (
           SELECT DISTINCT concat(payees.id, 'Vendor', vendor_amounts.year, payers.id) AS id,
              payees.id AS payee_id,
              payees.name AS payee_name,
              'Vendor'::text AS payee_type,
              payers.id AS payer_id,
              payers.name AS payer_name,
              vendor_amounts.amount_cents AS payments_total_cents,
              vendor_amounts.year
             FROM ((taxes_annual_vendor_amounts vendor_amounts
               JOIN companies payers ON ((vendor_amounts.company_id = payers.id)))
               JOIN vendors payees ON ((vendor_amounts.vendor_id = payees.id)))
          ), payees_above_threshhold AS (
           SELECT owner_payees.id,
              owner_payees.payee_id,
              owner_payees.payee_name,
              owner_payees.payee_type,
              owner_payees.payer_id,
              owner_payees.payer_name,
              owner_payees.payments_total_cents,
              owner_payees.year
             FROM owner_payees
          UNION
           SELECT vendor_payees.id,
              vendor_payees.payee_id,
              vendor_payees.payee_name,
              vendor_payees.payee_type,
              vendor_payees.payer_id,
              vendor_payees.payer_name,
              vendor_payees.payments_total_cents,
              vendor_payees.year
             FROM vendor_payees
          ), adjustment_payees AS (
           SELECT concat(tifs.payee_id, tifs.payee_type, tifs.filing_period, tifs.payer_id) AS id,
              tifs.payer_id,
              tifs.payee_id,
              tifs.payee_type,
              tifs.filing_period AS year,
              tifs.adjustment_cents,
              tifs.id AS taxes_irs_filing_id,
              payers.name AS payer_name,
              COALESCE(owner_payees.name, vendor_payees.name) AS payee_name
             FROM (((taxes_irs_filings tifs
               JOIN companies payers ON ((payers.id = tifs.payer_id)))
               LEFT JOIN companies owner_payees ON (((owner_payees.id = tifs.payee_id) AND ((tifs.payee_type)::text = 'Company'::text))))
               LEFT JOIN vendors vendor_payees ON (((vendor_payees.id = tifs.payee_id) AND ((tifs.payee_type)::text = 'Vendor'::text))))
            WHERE ((owner_payees.id IS NOT NULL) OR (vendor_payees.id IS NOT NULL))
          ), payees AS (
           SELECT COALESCE(pat.id, ap.id) AS id,
              COALESCE((pat.payer_id)::bigint, ap.payer_id) AS payer_id,
              COALESCE((pat.payee_id)::bigint, ap.payee_id) AS payee_id,
              COALESCE(pat.year, ap.year) AS year,
              COALESCE(pat.payee_type, (ap.payee_type)::text) AS payee_type,
              COALESCE(pat.payments_total_cents, (0)::double precision) AS payments_total_cents,
              COALESCE(ap.adjustment_cents, 0) AS adjustment_cents,
              COALESCE(ap.payer_name, pat.payer_name) AS payer_name,
              COALESCE(ap.payee_name, pat.payee_name) AS payee_name,
              ap.taxes_irs_filing_id
             FROM (payees_above_threshhold pat
               FULL JOIN adjustment_payees ap ON (((pat.payer_id = ap.payer_id) AND (pat.payee_id = ap.payee_id) AND (pat.year = ap.year) AND (pat.payee_type = (ap.payee_type)::text))))
            WHERE ((pat.payments_total_cents > (59999)::double precision) OR (ap.taxes_irs_filing_id IS NOT NULL))
          ), active_submission_ids AS (
           SELECT payees.id,
              payees.taxes_irs_filing_id,
              filings.form_name,
              max(submissions.id) AS submission_id
             FROM ((payees
               LEFT JOIN taxes_irs_filings filings ON (((filings.filing_period = payees.year) AND ((filings.payee_type)::text = payees.payee_type) AND (filings.payee_id = payees.payee_id) AND (filings.payer_id = payees.payer_id))))
               LEFT JOIN taxes_nelco_submissions submissions ON ((payees.taxes_irs_filing_id = submissions.taxes_irs_filing_id)))
            GROUP BY payees.id, payees.taxes_irs_filing_id, filings.form_name
          ), statused AS (
           SELECT payees.id,
              payees.payer_id,
              payees.payee_id,
              payees.year,
              payees.payee_type,
              payees.payments_total_cents,
              payees.adjustment_cents,
              payees.payer_name,
              payees.payee_name,
              payees.taxes_irs_filing_id,
                  CASE
                      WHEN (active_submission_ids.form_name IS NOT NULL) THEN active_submission_ids.form_name
                      WHEN (payees.payee_type = 'Company'::text) THEN '1099MISC'::character varying
                      WHEN (payees.payee_type = 'Vendor'::text) THEN '1099NEC'::character varying
                      ELSE NULL::character varying
                  END AS form_name,
                  CASE
                      WHEN (bg.status = 2) THEN su.status
                      WHEN (bg.status IS NULL) THEN 0
                      ELSE bg.status
                  END AS status,
              su.id AS active_submission_id,
              su.status AS active_submission_status
             FROM ((((payees
               JOIN active_submission_ids ON ((payees.id = active_submission_ids.id)))
               LEFT JOIN taxes_nelco_submissions su ON ((active_submission_ids.submission_id = su.id)))
               LEFT JOIN taxes_nelco_batches ba ON ((ba.id = su.taxes_nelco_batch_id)))
               LEFT JOIN taxes_batch_groups bg ON ((bg.id = ba.taxes_batch_group_id)))
          ), payee_address_ids AS (
           SELECT min(addresses.id) AS payee_address_id,
              statused_1.id
             FROM (statused statused_1
               LEFT JOIN addresses ON (((addresses.addressable_id = statused_1.payee_id) AND ((addresses.addressable_type)::text = statused_1.payee_type))))
            GROUP BY statused_1.id
          ), payee_taxpayer_ids AS (
           SELECT min(tids.id) AS taxpayer_identification_id,
              statused_1.id
             FROM (statused statused_1
               LEFT JOIN taxpayer_identifications tids ON (((tids.taxpayer_id = statused_1.payee_id) AND ((tids.taxpayer_type)::text = statused_1.payee_type))))
            GROUP BY statused_1.id
          ), payee_contact_ids AS (
           SELECT statused_1.id,
              min(COALESCE((vendor_contacts.id)::bigint, ownerships.owner_id)) AS contact_id
             FROM ((statused statused_1
               LEFT JOIN ownerships ON (((ownerships.entity_id = statused_1.payee_id) AND ((ownerships.owner_type)::text = 'Owner'::text) AND (statused_1.payee_type = 'Company'::text))))
               LEFT JOIN vendor_contacts ON (((vendor_contacts.vendor_id = statused_1.payee_id) AND (statused_1.payee_type = 'Vendor'::text))))
            GROUP BY statused_1.id
          ), issue_ids AS (
           SELECT statused_1.id,
              min(payee_taxpayer_ids.taxpayer_identification_id) AS taxpayer_identification_id,
              min(payee_address_ids.payee_address_id) AS address_id,
              min(payee_contact_ids.contact_id) AS contact_id
             FROM (((statused statused_1
               JOIN payee_taxpayer_ids ON ((statused_1.id = payee_taxpayer_ids.id)))
               JOIN payee_address_ids ON ((statused_1.id = payee_address_ids.id)))
               JOIN payee_contact_ids ON ((statused_1.id = payee_contact_ids.id)))
            GROUP BY statused_1.id, payee_taxpayer_ids.taxpayer_identification_id, payee_address_ids.payee_address_id, payee_contact_ids.contact_id
          )
   SELECT statused.id,
      statused.payer_id,
      statused.payee_id,
      statused.year,
      statused.payee_type,
      statused.payments_total_cents,
      statused.adjustment_cents,
      statused.payer_name,
      statused.payee_name,
      statused.taxes_irs_filing_id,
      statused.form_name,
      statused.status,
      statused.active_submission_id,
      statused.active_submission_status,
      issue_ids.address_id,
      issue_ids.taxpayer_identification_id,
      issue_ids.contact_id
     FROM (statused
       JOIN issue_ids ON ((issue_ids.id = statused.id)))
    ORDER BY statused.payee_type, statused.payee_id;
  SQL
  create_view "lease_computed_amounts", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), primary_rent_accounts AS (
           SELECT charge_presets.account_id AS id
             FROM charge_presets
            WHERE (charge_presets.kind = 5)
          ), assistance_rent_accounts AS (
           SELECT plutus_accounts.id
             FROM plutus_accounts
            WHERE ((plutus_accounts.category)::text = 'Rent Assistance Income'::text)
          ), all_rent_accounts AS (
           SELECT plutus_accounts.id
             FROM plutus_accounts
            WHERE (((plutus_accounts.category)::text = 'Rent Income'::text) OR (plutus_accounts.id IN ( SELECT assistance_rent_accounts.id
                     FROM assistance_rent_accounts)) OR (plutus_accounts.id IN ( SELECT primary_rent_accounts.id
                     FROM primary_rent_accounts)))
          ), secondary_rent_accounts AS (
           SELECT all_rent_accounts.id
             FROM all_rent_accounts
            WHERE ((NOT (all_rent_accounts.id IN ( SELECT primary_rent_accounts.id
                     FROM primary_rent_accounts))) AND (NOT (all_rent_accounts.id IN ( SELECT assistance_rent_accounts.id
                     FROM assistance_rent_accounts))))
          ), charge_schedule_aggregate_recurring_amounts AS (
           SELECT charge_schedules.chargeable_id AS lease_id,
              sum(active_recurring_charge_schedule_entries.amount_cents) AS total_monthly_amount_cents,
              sum(
                  CASE
                      WHEN (active_recurring_charge_schedule_entries.account_id IN ( SELECT primary_rent_accounts.id
                         FROM primary_rent_accounts)) THEN active_recurring_charge_schedule_entries.amount_cents
                      ELSE 0
                  END) AS monthly_primary_rent_cents,
              sum(
                  CASE
                      WHEN (active_recurring_charge_schedule_entries.account_id IN ( SELECT assistance_rent_accounts.id
                         FROM assistance_rent_accounts)) THEN active_recurring_charge_schedule_entries.amount_cents
                      ELSE 0
                  END) AS monthly_assistance_rent_cents,
              sum(
                  CASE
                      WHEN (active_recurring_charge_schedule_entries.account_id IN ( SELECT secondary_rent_accounts.id
                         FROM secondary_rent_accounts)) THEN active_recurring_charge_schedule_entries.amount_cents
                      ELSE 0
                  END) AS monthly_secondary_rent_cents,
              sum(
                  CASE
                      WHEN (active_recurring_charge_schedule_entries.account_id IN ( SELECT all_rent_accounts.id
                         FROM all_rent_accounts)) THEN active_recurring_charge_schedule_entries.amount_cents
                      ELSE 0
                  END) AS monthly_rent_cents
             FROM (((charge_schedules
               JOIN effective_date ON (true))
               JOIN leases leases_1 ON (((charge_schedules.chargeable_id = leases_1.id) AND ((charge_schedules.chargeable_type)::text = 'Lease'::text))))
               LEFT JOIN charge_schedule_entries active_recurring_charge_schedule_entries ON (((active_recurring_charge_schedule_entries.charge_schedule_id = charge_schedules.id) AND (active_recurring_charge_schedule_entries.recurring = true) AND (active_recurring_charge_schedule_entries.start_date <= LEAST(GREATEST(effective_date.date, leases_1.start_date), leases_1.end_date)) AND (active_recurring_charge_schedule_entries.end_date >= LEAST(GREATEST(effective_date.date, leases_1.start_date), leases_1.end_date)))))
            GROUP BY charge_schedules.id
          ), charge_schedule_aggregate_one_time_amounts AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              sum(one_time_charge_schedule_entries.amount_cents) AS move_in_costs_cents
             FROM (((charge_schedules
               JOIN leases leases_1 ON (((leases_1.id = charge_schedules.chargeable_id) AND ((charge_schedules.chargeable_type)::text = 'Lease'::text))))
               JOIN lease_chains lease_chains_1 ON ((leases_1.id = ANY (lease_chains_1.lease_ids))))
               LEFT JOIN charge_schedule_entries one_time_charge_schedule_entries ON (((one_time_charge_schedule_entries.charge_schedule_id = charge_schedules.id) AND (one_time_charge_schedule_entries.recurring = false))))
            GROUP BY lease_chains_1.id
          ), earliest_recurring_rent_charge_entry_dates AS (
           SELECT charge_schedules.chargeable_id AS lease_id,
              min(rent_entries.start_date) AS date
             FROM (charge_schedules
               LEFT JOIN charge_schedule_entries rent_entries ON (((rent_entries.charge_schedule_id = charge_schedules.id) AND (rent_entries.recurring = true) AND (rent_entries.account_id IN ( SELECT primary_rent_accounts.id
                     FROM primary_rent_accounts)))))
            GROUP BY charge_schedules.chargeable_id
          ), base_rent_amounts AS (
           SELECT charge_schedules.chargeable_id AS lease_id,
              sum(base_rent_entries.amount_cents) AS base_rent_cents
             FROM ((charge_schedules
               LEFT JOIN earliest_recurring_rent_charge_entry_dates ON ((earliest_recurring_rent_charge_entry_dates.lease_id = charge_schedules.chargeable_id)))
               LEFT JOIN charge_schedule_entries base_rent_entries ON (((base_rent_entries.charge_schedule_id = charge_schedules.id) AND (base_rent_entries.start_date = earliest_recurring_rent_charge_entry_dates.date) AND (base_rent_entries.account_id IN ( SELECT primary_rent_accounts.id
                     FROM primary_rent_accounts)))))
            GROUP BY charge_schedules.chargeable_id
          ), security_deposit_amounts AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              sum(charge_schedule_entries.amount_cents) AS security_deposit_cents
             FROM (((charge_schedules
               JOIN leases leases_1 ON (((leases_1.id = charge_schedules.chargeable_id) AND ((charge_schedules.chargeable_type)::text = 'Lease'::text))))
               JOIN lease_chains lease_chains_1 ON ((leases_1.id = ANY (lease_chains_1.lease_ids))))
               JOIN charge_schedule_entries ON (((charge_schedule_entries.charge_schedule_id = charge_schedules.id) AND (charge_schedule_entries.account_id IN ( SELECT charts_of_accounts.security_deposit_account_id
                     FROM charts_of_accounts)))))
            GROUP BY lease_chains_1.id
          )
   SELECT leases.id,
      leases.id AS lease_id,
      COALESCE(security_deposit_amounts.security_deposit_cents, (0)::bigint) AS security_deposit_cents,
      COALESCE(charge_schedule_aggregate_recurring_amounts.total_monthly_amount_cents, (0)::bigint) AS total_monthly_amount_cents,
      COALESCE(charge_schedule_aggregate_recurring_amounts.monthly_rent_cents, (0)::bigint) AS monthly_rent_cents,
      COALESCE(charge_schedule_aggregate_recurring_amounts.monthly_primary_rent_cents, (0)::bigint) AS monthly_primary_rent_cents,
      COALESCE(charge_schedule_aggregate_recurring_amounts.monthly_assistance_rent_cents, (0)::bigint) AS monthly_assistance_rent_cents,
      COALESCE(charge_schedule_aggregate_recurring_amounts.monthly_secondary_rent_cents, (0)::bigint) AS monthly_secondary_rent_cents,
      COALESCE((charge_schedule_aggregate_recurring_amounts.total_monthly_amount_cents - charge_schedule_aggregate_recurring_amounts.monthly_rent_cents), (0)::bigint) AS monthly_other_charges_cents,
      COALESCE(charge_schedule_aggregate_one_time_amounts.move_in_costs_cents, (0)::bigint) AS move_in_costs_cents,
      COALESCE(base_rent_amounts.base_rent_cents, (0)::bigint) AS base_rent_cents
     FROM (((((leases
       LEFT JOIN lease_chains ON ((leases.id = ANY (lease_chains.lease_ids))))
       LEFT JOIN charge_schedule_aggregate_recurring_amounts ON ((charge_schedule_aggregate_recurring_amounts.lease_id = leases.id)))
       LEFT JOIN base_rent_amounts ON ((base_rent_amounts.lease_id = leases.id)))
       LEFT JOIN charge_schedule_aggregate_one_time_amounts ON ((charge_schedule_aggregate_one_time_amounts.lease_chain_id = lease_chains.id)))
       LEFT JOIN security_deposit_amounts ON ((security_deposit_amounts.lease_chain_id = lease_chains.id)));
  SQL
  create_view "accounting_invoice_balances", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), effective_invoice_total_payments AS (
           SELECT invoices_1.id AS invoice_id,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_payments.reversed_at IS NULL) OR (invoice_payments.reversed_at > effective_date_1.date)) THEN invoice_payments.amount_cents
                      ELSE 0
                  END), (0)::bigint) AS payment
             FROM ((invoices invoices_1
               LEFT JOIN effective_date effective_date_1 ON (true))
               LEFT JOIN invoice_payments ON (((invoice_payments.invoice_id = invoices_1.id) AND (invoice_payments.date <= effective_date_1.date))))
            GROUP BY invoices_1.id
          )
   SELECT invoices.id,
      invoices.id AS invoice_id,
          CASE
              WHEN ((invoices.waived_at IS NULL) OR (invoices.waived_at > effective_date.date)) THEN (invoices.amount_cents - effective_invoice_total_payments.payment)
              ELSE (0)::bigint
          END AS balance
     FROM ((invoices
       JOIN effective_invoice_total_payments ON ((effective_invoice_total_payments.invoice_id = invoices.id)))
       LEFT JOIN effective_date ON (true))
    WHERE (invoices.post_date <= effective_date.date);
  SQL
  create_view "lease_chain_overdue_balance_breakdowns", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), rent_accounts AS (
           SELECT plutus_accounts.id AS account_id
             FROM plutus_accounts
            WHERE (((plutus_accounts.category)::text = 'Rent Income'::text) OR (plutus_accounts.id IN ( SELECT charge_presets.account_id
                     FROM charge_presets
                    WHERE (charge_presets.kind = 5))))
          ), assistance_rent_accounts AS (
           SELECT plutus_accounts.id AS account_id
             FROM plutus_accounts
            WHERE ((plutus_accounts.category)::text = 'Rent Assistance Income'::text)
          ), late_fee_accounts AS (
           SELECT charge_presets.account_id
             FROM charge_presets
            WHERE (charge_presets.kind = 4)
          ), late_invoices AS (
           SELECT invoices.id AS invoice_id,
              invoices.buyer_lease_membership_id,
              invoices.post_date,
              invoices.due_date
             FROM (invoices
               LEFT JOIN effective_date ON (true))
            WHERE ((invoices.due_date < effective_date.date) AND ((invoices.waived_at IS NULL) OR (invoices.waived_at > effective_date.date)))
          )
   SELECT lease_chain_id AS id,
      lease_chain_id,
      oldest_overdue_post_date,
      oldest_overdue_due_date,
      overdue_balance_cents,
      overdue_rent_cents,
      overdue_assistance_rent_cents,
      (overdue_balance_cents - overdue_assistance_rent_cents) AS overdue_resident_balance_cents,
      overdue_late_fees_cents,
      (((overdue_balance_cents - overdue_rent_cents) - overdue_assistance_rent_cents) - overdue_late_fees_cents) AS overdue_other_cents
     FROM ( SELECT lease_chains.id AS lease_chain_id,
              min(late_invoices.post_date) AS oldest_overdue_post_date,
              min(late_invoices.due_date) AS oldest_overdue_due_date,
              COALESCE(sum(line_item_receivable_balances.balance_cents), (0)::numeric) AS overdue_balance_cents,
              COALESCE(sum(
                  CASE
                      WHEN (line_items.receivable_account_id IN ( SELECT rent_accounts.account_id
                         FROM rent_accounts)) THEN line_item_receivable_balances.balance_cents
                      ELSE (0)::bigint
                  END), (0)::numeric) AS overdue_rent_cents,
              COALESCE(sum(
                  CASE
                      WHEN (line_items.receivable_account_id IN ( SELECT assistance_rent_accounts.account_id
                         FROM assistance_rent_accounts)) THEN line_item_receivable_balances.balance_cents
                      ELSE (0)::bigint
                  END), (0)::numeric) AS overdue_assistance_rent_cents,
              COALESCE(sum(
                  CASE
                      WHEN (line_items.receivable_account_id IN ( SELECT late_fee_accounts.account_id
                         FROM late_fee_accounts)) THEN line_item_receivable_balances.balance_cents
                      ELSE (0)::bigint
                  END), (0)::numeric) AS overdue_late_fees_cents
             FROM (((lease_chains
               LEFT JOIN late_invoices ON ((late_invoices.buyer_lease_membership_id = ANY (lease_chains.lease_membership_ids))))
               LEFT JOIN line_items ON ((line_items.invoice_id = late_invoices.invoice_id)))
               LEFT JOIN line_item_receivable_balances ON ((line_item_receivable_balances.line_item_id = line_items.id)))
            GROUP BY lease_chains.id) subquery;
  SQL
  create_view "accounting_persisted_ledgers", materialized: true, sql_definition: <<-SQL
      WITH most_recent_demand_letters AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              max(collections_demand_letters.id) AS id
             FROM (collections_demand_letters
               JOIN lease_chains lease_chains_1 ON ((collections_demand_letters.lease_id = ANY (lease_chains_1.lease_ids))))
            GROUP BY lease_chains_1.id
          ), most_recent_evictions AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              max(collections_evictions.id) AS id
             FROM (collections_evictions
               JOIN lease_chains lease_chains_1 ON ((collections_evictions.lease_id = ANY (lease_chains_1.lease_ids))))
            GROUP BY lease_chains_1.id
          ), most_recent_successful_payments AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              max(payments.created_at) AS created_at
             FROM (payments
               JOIN lease_chains lease_chains_1 ON ((payments.payer_lease_membership_id = ANY (lease_chains_1.lease_membership_ids))))
            WHERE ((payments.status <> 3) AND (payments.status <> 4))
            GROUP BY lease_chains_1.id
          ), lease_chain_tail_ids AS (
           SELECT lease_chains_1.id AS lease_chain_id,
              max(unnested_lease_ids.unnested_lease_ids) AS tail_id
             FROM lease_chains lease_chains_1,
              LATERAL unnest(lease_chains_1.lease_ids) unnested_lease_ids(unnested_lease_ids)
            GROUP BY lease_chains_1.id
          )
   SELECT lease_chains.id,
      lease_chains.id AS ledgerable_id,
      'Lease::Chain'::text AS ledgerable_type,
      lease_chains.start_date,
      lease_chains.end_date,
      properties.id AS property_id,
      units.id AS unit_id,
      lease_tails.id AS lease_id,
      primary_lease_memberships.tenant_id AS primary_tenant_id,
      breakdowns.overdue_balance_cents,
      breakdowns.overdue_rent_cents,
      breakdowns.overdue_assistance_rent_cents,
      breakdowns.overdue_resident_balance_cents,
      breakdowns.overdue_late_fees_cents,
      breakdowns.overdue_other_cents,
      breakdowns.oldest_overdue_post_date,
      breakdowns.oldest_overdue_due_date,
      most_recent_successful_payments.created_at AS most_recent_successful_payment_created_at,
      demand_letters.id AS demand_letter_id,
      demand_letters.created_at AS demand_letter_last_sent_at,
      demand_letters.expires_at AS demand_letter_expires_at,
      demand_letters.overdue_balance_cents AS demand_letter_balance_cents,
      evictions.id AS eviction_id
     FROM (((((((((((lease_chains
       LEFT JOIN lease_chain_tail_ids ON ((lease_chain_tail_ids.lease_chain_id = lease_chains.id)))
       LEFT JOIN leases lease_tails ON ((lease_tails.id = lease_chain_tail_ids.tail_id)))
       LEFT JOIN lease_memberships primary_lease_memberships ON (((primary_lease_memberships.lease_id = lease_tails.id) AND (primary_lease_memberships.role = 0))))
       LEFT JOIN units ON ((units.id = lease_tails.unit_id)))
       LEFT JOIN properties ON ((properties.id = units.property_id)))
       LEFT JOIN lease_chain_overdue_balance_breakdowns breakdowns ON ((breakdowns.lease_chain_id = lease_chains.id)))
       LEFT JOIN most_recent_demand_letters ON ((most_recent_demand_letters.lease_chain_id = lease_chains.id)))
       LEFT JOIN collections_demand_letters demand_letters ON ((demand_letters.id = most_recent_demand_letters.id)))
       LEFT JOIN most_recent_evictions ON ((most_recent_evictions.lease_chain_id = lease_chains.id)))
       LEFT JOIN collections_evictions evictions ON ((evictions.id = most_recent_evictions.id)))
       LEFT JOIN most_recent_successful_payments ON ((most_recent_successful_payments.lease_chain_id = lease_chains.id)));
  SQL
  create_view "accounting_property_cash_balances", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), debit_and_credit_amounts AS (
           SELECT DISTINCT amounts.id AS amount_id,
              amounts.type AS amount_type,
              amounts.amount,
              entries.property_id,
              entries.journal_id AS company_id,
              payments.status AS payment_status
             FROM (((((((plutus_amounts amounts
               JOIN plutus_accounts accounts ON ((amounts.account_id = accounts.id)))
               JOIN charts_of_accounts book ON (((accounts.tenant_id = book.id) AND ((accounts.tenant_type)::text = 'ChartOfAccounts'::text))))
               JOIN plutus_entries entries ON ((amounts.entry_id = entries.id)))
               LEFT JOIN payments ON (((entries.commercial_document_id = payments.id) AND ((entries.commercial_document_type)::text = 'Payment'::text))))
               LEFT JOIN effective_date ON (true))
               JOIN plutus_amounts associated_amounts ON ((entries.id = associated_amounts.entry_id)))
               JOIN plutus_accounts associated_amount_accounts ON ((associated_amounts.account_id = associated_amount_accounts.id)))
            WHERE ((((book.due_from_client_entity_account_id IS NOT NULL) AND (associated_amounts.account_id = book.due_from_client_entity_account_id)) OR ((book.prepaid_revenue_account_id IS NOT NULL) AND (associated_amounts.account_id = book.prepaid_revenue_account_id)) OR ((book.prepaid_expense_account_id IS NOT NULL) AND (associated_amounts.account_id = book.prepaid_expense_account_id))) AND (((book.due_from_client_entity_account_id IS NULL) OR (amounts.account_id <> book.due_from_client_entity_account_id)) AND ((book.prepaid_revenue_account_id IS NULL) OR (amounts.account_id <> book.prepaid_revenue_account_id)) AND ((book.prepaid_expense_account_id IS NULL) OR (amounts.account_id <> book.prepaid_expense_account_id))) AND (entries.date <= effective_date.date) AND (entries.basis = ANY (ARRAY[0, 3])))
          ), debit_and_credit_sums AS (
           SELECT debit_and_credit_amounts.property_id,
              debit_and_credit_amounts.company_id,
              sum(debit_and_credit_amounts.amount) FILTER (WHERE ((debit_and_credit_amounts.amount_type)::text = 'Plutus::CreditAmount'::text)) AS balance_credits,
              sum(debit_and_credit_amounts.amount) FILTER (WHERE (((debit_and_credit_amounts.amount_type)::text = 'Plutus::CreditAmount'::text) AND ((debit_and_credit_amounts.payment_status IS NULL) OR (debit_and_credit_amounts.payment_status = 0)))) AS transferrable_balance_credits,
              sum(debit_and_credit_amounts.amount) FILTER (WHERE ((debit_and_credit_amounts.amount_type)::text = 'Plutus::DebitAmount'::text)) AS balance_debits
             FROM debit_and_credit_amounts
            GROUP BY debit_and_credit_amounts.company_id, debit_and_credit_amounts.property_id
          )
   SELECT property_id AS id,
      property_id,
      (COALESCE(balance_credits, (0)::numeric) - COALESCE(balance_debits, (0)::numeric)) AS cash_balance,
      (COALESCE(transferrable_balance_credits, (0)::numeric) - COALESCE(balance_debits, (0)::numeric)) AS transferrable_cash_balance
     FROM debit_and_credit_sums;
  SQL
  create_view "lease_chain_aging_receivables_summaries", sql_definition: <<-SQL
      WITH effective_date AS (
           SELECT (COALESCE(NULLIF(current_setting('reporting_settings.effective_date'::text, true), ''::text), (CURRENT_DATE)::text))::date AS date
          ), lease_membership_ids_to_lease_chain_ids AS (
           SELECT lease_membership_id.lease_membership_id,
              lease_chains_1.id AS lease_chain_id
             FROM lease_chains lease_chains_1,
              LATERAL unnest(lease_chains_1.lease_membership_ids) lease_membership_id(lease_membership_id)
          ), effective_invoice_total_payments AS (
           SELECT invoices.id AS invoice_id,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_payments.reversed_at IS NULL) OR (invoice_payments.reversed_at > effective_date.date)) THEN invoice_payments.amount_cents
                      ELSE 0
                  END), (0)::bigint) AS payment
             FROM ((invoices
               LEFT JOIN effective_date ON (true))
               LEFT JOIN invoice_payments ON (((invoice_payments.invoice_id = invoices.id) AND (invoice_payments.date <= effective_date.date))))
            GROUP BY invoices.id
          ), invoice_balances AS (
           SELECT invoices.due_date,
                  CASE
                      WHEN ((invoices.waived_at IS NULL) OR (invoices.waived_at > effective_date.date)) THEN (invoices.amount_cents - effective_invoice_total_payments.payment)
                      ELSE (0)::bigint
                  END AS balance_cents,
              invoices.buyer_lease_membership_id AS lease_membership_id
             FROM ((invoices
               JOIN effective_invoice_total_payments ON ((effective_invoice_total_payments.invoice_id = invoices.id)))
               LEFT JOIN effective_date ON (true))
            WHERE ((invoices.post_date <= effective_date.date) AND (invoices.buyer_lease_membership_id IS NOT NULL))
          ), aged_invoice_balances AS (
           SELECT lease_membership_ids_to_lease_chain_ids.lease_chain_id,
              COALESCE(sum(
                  CASE
                      WHEN (invoice_balances.balance_cents > 0) THEN 1
                      ELSE 0
                  END), (0)::bigint) AS open_invoice_count,
              COALESCE(sum(invoice_balances.balance_cents), (0)::numeric) AS due_cents,
              COALESCE(sum(
                  CASE
                      WHEN (invoice_balances.due_date >= effective_date.date) THEN invoice_balances.balance_cents
                      ELSE NULL::bigint
                  END), (0)::numeric) AS current_cents,
              COALESCE(sum(
                  CASE
                      WHEN (invoice_balances.due_date < effective_date.date) THEN invoice_balances.balance_cents
                      ELSE NULL::bigint
                  END), (0)::numeric) AS overdue_cents,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_balances.due_date >= (effective_date.date - 'P30D'::interval)) AND (invoice_balances.due_date <= (effective_date.date - 'P1D'::interval))) THEN invoice_balances.balance_cents
                      ELSE NULL::bigint
                  END), (0)::numeric) AS one_to_thirty_days_cents,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_balances.due_date >= (effective_date.date - 'P60D'::interval)) AND (invoice_balances.due_date <= (effective_date.date - 'P31D'::interval))) THEN invoice_balances.balance_cents
                      ELSE NULL::bigint
                  END), (0)::numeric) AS thirty_one_to_sixty_days_cents,
              COALESCE(sum(
                  CASE
                      WHEN ((invoice_balances.due_date >= (effective_date.date - 'P90D'::interval)) AND (invoice_balances.due_date <= (effective_date.date - 'P61D'::interval))) THEN invoice_balances.balance_cents
                      ELSE NULL::bigint
                  END), (0)::numeric) AS sixty_one_to_ninety_days_cents,
              COALESCE(sum(
                  CASE
                      WHEN (invoice_balances.due_date <= (effective_date.date - 'P91D'::interval)) THEN invoice_balances.balance_cents
                      ELSE NULL::bigint
                  END), (0)::numeric) AS ninety_one_plus_days_cents
             FROM (((lease_memberships
               LEFT JOIN effective_date ON (true))
               JOIN lease_membership_ids_to_lease_chain_ids ON ((lease_membership_ids_to_lease_chain_ids.lease_membership_id = lease_memberships.id)))
               LEFT JOIN invoice_balances ON ((invoice_balances.lease_membership_id = lease_memberships.id)))
            GROUP BY lease_membership_ids_to_lease_chain_ids.lease_chain_id
          ), lease_chain_prepayment_balances AS (
           SELECT lease_membership_ids_to_lease_chain_ids.lease_chain_id,
              (COALESCE(sum(payments.amount_cents), (0)::bigint) - COALESCE(sum(invoice_payments.amount_cents), (0)::bigint)) AS prepaid_cents
             FROM ((((lease_memberships
               LEFT JOIN effective_date ON (true))
               JOIN lease_membership_ids_to_lease_chain_ids ON ((lease_membership_ids_to_lease_chain_ids.lease_membership_id = lease_memberships.id)))
               LEFT JOIN payments ON (((payments.payer_lease_membership_id = lease_memberships.id) AND (payments.date <= effective_date.date) AND ((payments.reversed_at IS NULL) OR (payments.reversed_at > effective_date.date)))))
               LEFT JOIN invoice_payments ON (((invoice_payments.payment_id = payments.id) AND (invoice_payments.date <= effective_date.date) AND ((invoice_payments.reversed_at IS NULL) OR (invoice_payments.reversed_at > effective_date.date)))))
            GROUP BY lease_membership_ids_to_lease_chain_ids.lease_chain_id
          )
   SELECT lease_chains.id,
      lease_chains.id AS lease_chain_id,
      lease_chains.current_unit_id AS unit_id,
      aged_invoice_balances.open_invoice_count,
      aged_invoice_balances.due_cents,
      aged_invoice_balances.current_cents,
      aged_invoice_balances.overdue_cents,
      aged_invoice_balances.one_to_thirty_days_cents,
      aged_invoice_balances.thirty_one_to_sixty_days_cents,
      aged_invoice_balances.sixty_one_to_ninety_days_cents,
      aged_invoice_balances.ninety_one_plus_days_cents,
      lease_chain_prepayment_balances.prepaid_cents,
      (aged_invoice_balances.due_cents - (lease_chain_prepayment_balances.prepaid_cents)::numeric) AS balance_cents
     FROM ((lease_chains
       LEFT JOIN aged_invoice_balances ON ((aged_invoice_balances.lease_chain_id = lease_chains.id)))
       LEFT JOIN lease_chain_prepayment_balances ON ((lease_chain_prepayment_balances.lease_chain_id = lease_chains.id)));
  SQL
end
